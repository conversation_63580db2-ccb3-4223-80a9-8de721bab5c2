spring.application.name=Service

# Database Configuration
spring.datasource.url=*****************************************************************************************************
spring.datasource.username=root
spring.datasource.password=uN1p!FL%zazDD&bOC2sSLR9#lbtPEu
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQLDialect
spring.jpa.properties.hibernate.format_sql=true

# Server Configuration
server.port=8006

# Redis Configuration
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.database=1


# Logging Configuration
logging.level.com.g4.service=INFO
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE


############## Sa-Token ?? (??: https://sa-token.cc) ##############

# token ??????? cookie ???
sa-token.token-name=token
# token ????????? ??30??-1 ??????
sa-token.timeout=2592000
# token ??????????????? token ???????????????????-1 ??????????
sa-token.active-timeout=-1
# ?????????????? ?? true ???????, ? false ??????????
sa-token.is-concurrent=true
# ????????????????? token ?? true ????????? token, ? false ????????? token?
sa-token.is-share=false
# token ?????????uuid?simple-uuid?random-32?random-64?random-128?tik?
sa-token.token-style=random-128
# ????????
sa-token.is-log=true

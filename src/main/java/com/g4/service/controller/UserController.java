package com.g4.service.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.g4.service.common.response.ApiResponse;
import com.g4.service.common.exception.BusinessException;
import com.g4.service.common.enums.ResponseCode;
import com.g4.service.util.StringUtil;
import com.g4.service.util.ValidationUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 用户控制器
 * 演示统一响应体和异常处理的使用
 */
@Slf4j
@RestController
@RequestMapping("/api/user")
public class UserController {

    /**
     * 用户登录
     * 测试地址：POST http://localhost:8006/api/user/login
     * 请求体：{"username": "zhang", "password": "123456"}
     */
    @PostMapping("/login")
    public ApiResponse<Map<String, Object>> login(@RequestBody Map<String, String> loginRequest) {
        try {
            String username = loginRequest.get("username");
            String password = loginRequest.get("password");

            // 参数校验
            ValidationUtil.notBlank(username, "用户名不能为空");
            ValidationUtil.notBlank(password, "密码不能为空");

            // 此处仅作模拟示例，真实项目需要从数据库中查询数据进行比对
            if ("zhang".equals(username) && "123456".equals(password)) {
                // 登录成功
                StpUtil.login(10001);

                Map<String, Object> data = new HashMap<>();
                data.put("userId", 10001);
                data.put("username", username);
                data.put("token", StpUtil.getTokenValue());
                data.put("tokenTimeout", StpUtil.getTokenTimeout());

                log.info("用户登录成功: {}", username);
                return ApiResponse.success("登录成功", data);
            } else {
                // 登录失败
                throw BusinessException.passwordError();
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("登录异常: ", e);
            throw new BusinessException(ResponseCode.INTERNAL_ERROR, "登录过程中发生异常");
        }
    }

    /**
     * 查询登录状态
     * 测试地址：GET http://localhost:8006/api/user/login-status
     */
    @GetMapping("/login-status")
    public ApiResponse<Map<String, Object>> getLoginStatus() {
        Map<String, Object> data = new HashMap<>();
        data.put("isLogin", StpUtil.isLogin());

        if (StpUtil.isLogin()) {
            data.put("userId", StpUtil.getLoginId());
            data.put("tokenValue", StpUtil.getTokenValue());
            data.put("tokenTimeout", StpUtil.getTokenTimeout());
        }

        return ApiResponse.success(data);
    }

    /**
     * 用户登出
     * 测试地址：POST http://localhost:8006/api/user/logout
     */
    @PostMapping("/logout")
    public ApiResponse<Void> logout() {
        if (StpUtil.isLogin()) {
            Object userId = StpUtil.getLoginId();
            StpUtil.logout();
            log.info("用户登出成功: {}", userId);
            return ApiResponse.success("登出成功", null);
        } else {
            return ApiResponse.success("用户未登录", null);
        }
    }

    /**
     * 获取用户信息（需要登录）
     * 测试地址：GET http://localhost:8006/api/user/info
     */
    @GetMapping("/info")
    public ApiResponse<Map<String, Object>> getUserInfo() {
        // Sa-Token会自动校验登录状态，未登录会抛出NotLoginException
        StpUtil.checkLogin();

        Object userId = StpUtil.getLoginId();

        // 模拟从数据库查询用户信息
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("userId", userId);
        userInfo.put("username", "zhang");
        userInfo.put("nickname", "张三");
        userInfo.put("email", "<EMAIL>");
        userInfo.put("phone", "138****5678");

        return ApiResponse.success(userInfo);
    }

    /**
     * 参数校验示例
     * 测试地址：POST http://localhost:8006/api/user/validate-demo
     */
    @PostMapping("/validate-demo")
    public ApiResponse<String> validateDemo(@RequestParam String email, @RequestParam String phone) {
        // 使用ValidationUtil进行参数校验
        ValidationUtil.validEmail(email, "邮箱格式不正确");
        ValidationUtil.validPhone(phone, "手机号格式不正确");

        return ApiResponse.success("参数校验通过");
    }

    /**
     * 业务异常示例
     * 测试地址：GET http://localhost:8006/api/user/business-error-demo
     */
    @GetMapping("/business-error-demo")
    public ApiResponse<Void> businessErrorDemo(@RequestParam(defaultValue = "1") Integer type) {
        switch (type) {
            case 1:
                throw BusinessException.userNotFound();
            case 2:
                throw BusinessException.dataAlreadyExists("用户已存在");
            case 3:
                throw new BusinessException(ResponseCode.OPERATION_NOT_ALLOWED, "当前操作不被允许");
            default:
                return ApiResponse.success();
        }
    }
}

package com.g4.service.interceptor;

import cn.dev33.satoken.stp.StpUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Map;

/**
 * 请求日志拦截器
 * 记录请求和响应的详细信息
 */
@Slf4j
@Component
public class LogInterceptor implements HandlerInterceptor {

    @Autowired
    private ObjectMapper objectMapper;

    private static final String START_TIME = "startTime";

    /**
     * 请求前处理
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 记录开始时间
        request.setAttribute(START_TIME, System.currentTimeMillis());

        // 包装请求和响应以便读取内容
        if (!(request instanceof ContentCachingRequestWrapper)) {
            request = new ContentCachingRequestWrapper(request);
        }
        if (!(response instanceof ContentCachingResponseWrapper)) {
            response = new ContentCachingResponseWrapper(response);
        }

        // 记录请求信息
        logRequest(request);

        return true;
    }

    /**
     * 请求后处理
     */
    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 可以在这里添加一些后处理逻辑
    }

    /**
     * 请求完成后处理
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 计算处理时间
        Long startTime = (Long) request.getAttribute(START_TIME);
        long duration = System.currentTimeMillis() - startTime;

        // 记录响应信息
        logResponse(request, response, duration, ex);
    }

    /**
     * 记录请求信息
     */
    private void logRequest(HttpServletRequest request) {
        try {
            Map<String, Object> requestInfo = new HashMap<>();
            requestInfo.put("method", request.getMethod());
            requestInfo.put("uri", request.getRequestURI());
            requestInfo.put("queryString", request.getQueryString());
            requestInfo.put("remoteAddr", getClientIpAddress(request));
            requestInfo.put("userAgent", request.getHeader("User-Agent"));
            requestInfo.put("contentType", request.getContentType());

            // 获取当前用户ID
            try {
                if (StpUtil.isLogin()) {
                    requestInfo.put("userId", StpUtil.getLoginId());
                }
            } catch (Exception e) {
                // 忽略未登录异常
            }

            // 获取请求头
            Map<String, String> headers = new HashMap<>();
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                // 过滤敏感头信息
                if (!isSensitiveHeader(headerName)) {
                    headers.put(headerName, request.getHeader(headerName));
                }
            }
            requestInfo.put("headers", headers);

            // 获取请求参数
            Map<String, String[]> parameters = request.getParameterMap();
            if (!parameters.isEmpty()) {
                Map<String, Object> params = new HashMap<>();
                for (Map.Entry<String, String[]> entry : parameters.entrySet()) {
                    String key = entry.getKey();
                    String[] values = entry.getValue();
                    if (values.length == 1) {
                        params.put(key, isSensitiveParam(key) ? "***" : values[0]);
                    } else {
                        params.put(key, isSensitiveParam(key) ? "***" : values);
                    }
                }
                requestInfo.put("parameters", params);
            }

            // 获取请求体（仅对POST、PUT等方法）
            if (request instanceof ContentCachingRequestWrapper && 
                ("POST".equals(request.getMethod()) || "PUT".equals(request.getMethod()) || "PATCH".equals(request.getMethod()))) {
                ContentCachingRequestWrapper wrapper = (ContentCachingRequestWrapper) request;
                byte[] content = wrapper.getContentAsByteArray();
                if (content.length > 0) {
                    String body = new String(content, StandardCharsets.UTF_8);
                    // 限制请求体长度
                    if (body.length() > 1000) {
                        body = body.substring(0, 1000) + "...";
                    }
                    requestInfo.put("body", body);
                }
            }

            log.info("请求信息: {}", objectMapper.writeValueAsString(requestInfo));
        } catch (Exception e) {
            log.error("记录请求信息失败", e);
        }
    }

    /**
     * 记录响应信息
     */
    private void logResponse(HttpServletRequest request, HttpServletResponse response, long duration, Exception ex) {
        try {
            Map<String, Object> responseInfo = new HashMap<>();
            responseInfo.put("method", request.getMethod());
            responseInfo.put("uri", request.getRequestURI());
            responseInfo.put("status", response.getStatus());
            responseInfo.put("duration", duration + "ms");
            responseInfo.put("contentType", response.getContentType());

            // 获取当前用户ID
            try {
                if (StpUtil.isLogin()) {
                    responseInfo.put("userId", StpUtil.getLoginId());
                }
            } catch (Exception e) {
                // 忽略未登录异常
            }

            // 记录异常信息
            if (ex != null) {
                responseInfo.put("exception", ex.getClass().getSimpleName());
                responseInfo.put("exceptionMessage", ex.getMessage());
            }

            // 获取响应体（仅在开发环境或调试模式下）
            if (response instanceof ContentCachingResponseWrapper) {
                ContentCachingResponseWrapper wrapper = (ContentCachingResponseWrapper) response;
                byte[] content = wrapper.getContentAsByteArray();
                if (content.length > 0 && content.length < 1000) {
                    String body = new String(content, StandardCharsets.UTF_8);
                    responseInfo.put("body", body);
                }
                // 重要：复制内容到原始响应
                wrapper.copyBodyToResponse();
            }

            // 根据响应状态选择日志级别
            if (response.getStatus() >= 500) {
                log.error("响应信息: {}", objectMapper.writeValueAsString(responseInfo));
            } else if (response.getStatus() >= 400) {
                log.warn("响应信息: {}", objectMapper.writeValueAsString(responseInfo));
            } else {
                log.info("响应信息: {}", objectMapper.writeValueAsString(responseInfo));
            }
        } catch (Exception e) {
            log.error("记录响应信息失败", e);
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 判断是否为敏感请求头
     */
    private boolean isSensitiveHeader(String headerName) {
        String lowerCaseName = headerName.toLowerCase();
        return lowerCaseName.contains("authorization") || 
               lowerCaseName.contains("token") || 
               lowerCaseName.contains("password") ||
               lowerCaseName.contains("secret");
    }

    /**
     * 判断是否为敏感参数
     */
    private boolean isSensitiveParam(String paramName) {
        String lowerCaseName = paramName.toLowerCase();
        return lowerCaseName.contains("password") || 
               lowerCaseName.contains("token") || 
               lowerCaseName.contains("secret") ||
               lowerCaseName.contains("key");
    }
}

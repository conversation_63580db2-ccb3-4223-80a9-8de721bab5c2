package com.g4.service.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统一响应状态码枚举
 * HTTP状态码统一为200，通过code字段区分具体错误类型
 */
@Getter
@AllArgsConstructor
public enum ResponseCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 (2xx)
    PARAM_ERROR(201, "参数错误"),
    VALIDATION_ERROR(202, "数据校验失败"),
    BUSINESS_ERROR(203, "业务逻辑错误"),
    DATA_NOT_FOUND(204, "数据不存在"),
    DATA_ALREADY_EXISTS(205, "数据已存在"),
    OPERATION_NOT_ALLOWED(206, "操作不被允许"),
    
    // 认证授权错误 (4xx)
    UNAUTHORIZED(401, "未认证，请先登录"),
    FORBIDDEN(403, "权限不足，拒绝访问"),
    TOKEN_EXPIRED(402, "登录已过期，请重新登录"),
    TOKEN_INVALID(404, "无效的访问令牌"),
    
    // 服务器错误 (5xx)
    INTERNAL_ERROR(500, "服务器内部错误"),
    DATABASE_ERROR(501, "数据库操作失败"),
    NETWORK_ERROR(502, "网络连接异常"),
    SERVICE_UNAVAILABLE(503, "服务暂不可用"),
    
    // 业务特定错误 (6xx)
    USER_NOT_FOUND(601, "用户不存在"),
    USER_ALREADY_EXISTS(602, "用户已存在"),
    PASSWORD_ERROR(603, "密码错误"),
    ACCOUNT_DISABLED(604, "账户已被禁用"),
    ACCOUNT_LOCKED(605, "账户已被锁定"),
    
    // 店铺相关错误 (7xx)
    SHOP_NOT_FOUND(701, "店铺不存在"),
    SHOP_ALREADY_EXISTS(702, "店铺已存在"),
    SHOP_STATUS_ERROR(703, "店铺状态异常"),
    
    // 商品相关错误 (8xx)
    PRODUCT_NOT_FOUND(801, "商品不存在"),
    PRODUCT_OUT_OF_STOCK(802, "商品库存不足"),
    PRODUCT_STATUS_ERROR(803, "商品状态异常");
    
    private final int code;
    private final String message;
    
    /**
     * 根据code获取对应的枚举
     */
    public static ResponseCode getByCode(int code) {
        for (ResponseCode responseCode : values()) {
            if (responseCode.getCode() == code) {
                return responseCode;
            }
        }
        return INTERNAL_ERROR;
    }
}

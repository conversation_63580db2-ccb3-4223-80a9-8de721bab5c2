package com.g4.service.common.exception;

import com.g4.service.common.enums.ResponseCode;
import lombok.Getter;

/**
 * 业务异常类
 * 用于处理业务逻辑中的异常情况
 */
@Getter
public class BusinessException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 错误码
     */
    private final Integer code;
    
    /**
     * 错误消息
     */
    private final String message;
    
    /**
     * 附加数据
     */
    private final Object data;
    
    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
        this.data = null;
    }
    
    public BusinessException(Integer code, String message, Object data) {
        super(message);
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    public BusinessException(ResponseCode responseCode) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.data = null;
    }
    
    public BusinessException(ResponseCode responseCode, Object data) {
        super(responseCode.getMessage());
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.data = data;
    }
    
    public BusinessException(ResponseCode responseCode, String customMessage) {
        super(customMessage);
        this.code = responseCode.getCode();
        this.message = customMessage;
        this.data = null;
    }
    
    public BusinessException(ResponseCode responseCode, String customMessage, Object data) {
        super(customMessage);
        this.code = responseCode.getCode();
        this.message = customMessage;
        this.data = data;
    }
    
    // ========== 便捷的静态方法 ==========
    
    /**
     * 参数错误
     */
    public static BusinessException paramError(String message) {
        return new BusinessException(ResponseCode.PARAM_ERROR, message);
    }
    
    /**
     * 数据不存在
     */
    public static BusinessException dataNotFound(String message) {
        return new BusinessException(ResponseCode.DATA_NOT_FOUND, message);
    }
    
    /**
     * 数据已存在
     */
    public static BusinessException dataAlreadyExists(String message) {
        return new BusinessException(ResponseCode.DATA_ALREADY_EXISTS, message);
    }
    
    /**
     * 操作不被允许
     */
    public static BusinessException operationNotAllowed(String message) {
        return new BusinessException(ResponseCode.OPERATION_NOT_ALLOWED, message);
    }
    
    /**
     * 用户不存在
     */
    public static BusinessException userNotFound() {
        return new BusinessException(ResponseCode.USER_NOT_FOUND);
    }
    
    /**
     * 用户已存在
     */
    public static BusinessException userAlreadyExists() {
        return new BusinessException(ResponseCode.USER_ALREADY_EXISTS);
    }
    
    /**
     * 密码错误
     */
    public static BusinessException passwordError() {
        return new BusinessException(ResponseCode.PASSWORD_ERROR);
    }
    
    /**
     * 店铺不存在
     */
    public static BusinessException shopNotFound() {
        return new BusinessException(ResponseCode.SHOP_NOT_FOUND);
    }
    
    /**
     * 商品不存在
     */
    public static BusinessException productNotFound() {
        return new BusinessException(ResponseCode.PRODUCT_NOT_FOUND);
    }
    
    /**
     * 商品库存不足
     */
    public static BusinessException productOutOfStock() {
        return new BusinessException(ResponseCode.PRODUCT_OUT_OF_STOCK);
    }
}

package com.g4.service.common.exception;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.exception.NotPermissionException;
import cn.dev33.satoken.exception.NotRoleException;
import com.g4.service.common.enums.ResponseCode;
import com.g4.service.common.response.ApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 全局异常处理器
 * 统一处理系统中的各种异常，返回标准格式的响应
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ResponseEntity<ApiResponse<Object>> handleBusinessException(BusinessException e, HttpServletRequest request) {
        log.warn("业务异常: {} - {}", e.getCode(), e.getMessage());
        ApiResponse<Object> response = ApiResponse.error(e.getCode(), e.getMessage(), e.getData());
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理参数校验异常 - @RequestBody参数校验
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleValidationException(MethodArgumentNotValidException e) {
        log.warn("参数校验失败: {}", e.getMessage());
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        ApiResponse<Map<String, String>> response = ApiResponse.error(ResponseCode.VALIDATION_ERROR, errors);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理参数绑定异常 - @ModelAttribute参数校验
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleBindException(BindException e) {
        log.warn("参数绑定失败: {}", e.getMessage());
        Map<String, String> errors = new HashMap<>();
        e.getBindingResult().getAllErrors().forEach(error -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            errors.put(fieldName, errorMessage);
        });
        ApiResponse<Map<String, String>> response = ApiResponse.error(ResponseCode.VALIDATION_ERROR, errors);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理约束违反异常 - @Validated参数校验
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Map<String, String>>> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("约束违反: {}", e.getMessage());
        Map<String, String> errors = new HashMap<>();
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        for (ConstraintViolation<?> violation : violations) {
            String fieldName = violation.getPropertyPath().toString();
            String errorMessage = violation.getMessage();
            errors.put(fieldName, errorMessage);
        }
        ApiResponse<Map<String, String>> response = ApiResponse.error(ResponseCode.VALIDATION_ERROR, errors);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理缺少请求参数异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public ResponseEntity<ApiResponse<Object>> handleMissingParameterException(MissingServletRequestParameterException e) {
        log.warn("缺少请求参数: {}", e.getMessage());
        String message = String.format("缺少必需的请求参数: %s", e.getParameterName());
        ApiResponse<Object> response = ApiResponse.paramError(message);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理参数类型不匹配异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Object>> handleTypeMismatchException(MethodArgumentTypeMismatchException e) {
        log.warn("参数类型不匹配: {}", e.getMessage());
        String message = String.format("参数 %s 的值 %s 类型不正确", e.getName(), e.getValue());
        ApiResponse<Object> response = ApiResponse.paramError(message);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理Sa-Token未登录异常
     */
    @ExceptionHandler(NotLoginException.class)
    public ResponseEntity<ApiResponse<Object>> handleNotLoginException(NotLoginException e) {
        log.warn("用户未登录: {}", e.getMessage());
        ApiResponse<Object> response;
        switch (e.getType()) {
            case NotLoginException.NOT_TOKEN:
                response = ApiResponse.error(ResponseCode.UNAUTHORIZED, "未提供访问令牌");
                break;
            case NotLoginException.INVALID_TOKEN:
                response = ApiResponse.error(ResponseCode.TOKEN_INVALID, "访问令牌无效");
                break;
            case NotLoginException.TOKEN_TIMEOUT:
                response = ApiResponse.error(ResponseCode.TOKEN_EXPIRED, "访问令牌已过期");
                break;
            default:
                response = ApiResponse.unauthorized();
        }
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理Sa-Token权限不足异常
     */
    @ExceptionHandler(NotPermissionException.class)
    public ResponseEntity<ApiResponse<Object>> handleNotPermissionException(NotPermissionException e) {
        log.warn("权限不足: {}", e.getMessage());
        ApiResponse<Object> response = ApiResponse.forbidden();
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理Sa-Token角色不足异常
     */
    @ExceptionHandler(NotRoleException.class)
    public ResponseEntity<ApiResponse<Object>> handleNotRoleException(NotRoleException e) {
        log.warn("角色不足: {}", e.getMessage());
        ApiResponse<Object> response = ApiResponse.forbidden();
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理数据库访问异常
     */
    @ExceptionHandler(DataAccessException.class)
    public ResponseEntity<ApiResponse<Object>> handleDataAccessException(DataAccessException e) {
        log.error("数据库访问异常: ", e);
        ApiResponse<Object> response = ApiResponse.error(ResponseCode.DATABASE_ERROR);
        return ResponseEntity.ok(response);
    }
    
    /**
     * 处理其他未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Object>> handleGenericException(Exception e, HttpServletRequest request) {
        log.error("系统异常: {} - {}", request.getRequestURI(), e.getMessage(), e);
        ApiResponse<Object> response = ApiResponse.internalError();
        return ResponseEntity.ok(response);
    }
}

package com.g4.service.common;

/**
 * 系统常量类
 * 定义系统中使用的常量
 */
public final class Constants {

    private Constants() {
        // 私有构造函数，防止实例化
    }

    /**
     * 系统相关常量
     */
    public static final class System {
        public static final String DEFAULT_CHARSET = "UTF-8";
        public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";
        public static final String DEFAULT_LOCALE = "zh_CN";
        public static final String APPLICATION_NAME = "shop-service";
        public static final String VERSION = "1.0.0";
    }

    /**
     * 缓存相关常量
     */
    public static final class Cache {
        public static final String USER_PREFIX = "user:";
        public static final String ROLE_PREFIX = "role:";
        public static final String PERMISSION_PREFIX = "permission:";
        public static final String SHOP_PREFIX = "shop:";
        public static final String PRODUCT_PREFIX = "product:";
        public static final String CATEGORY_PREFIX = "category:";
        public static final String TOKEN_PREFIX = "token:";
        public static final String CAPTCHA_PREFIX = "captcha:";
        public static final String RATE_LIMIT_PREFIX = "rate_limit:";
        
        // 缓存过期时间（秒）
        public static final int DEFAULT_EXPIRE = 1800; // 30分钟
        public static final int SHORT_EXPIRE = 300;    // 5分钟
        public static final int LONG_EXPIRE = 86400;   // 24小时
        public static final int TOKEN_EXPIRE = 7200;   // 2小时
        public static final int CAPTCHA_EXPIRE = 300;  // 5分钟
    }

    /**
     * 用户相关常量
     */
    public static final class User {
        public static final String DEFAULT_PASSWORD = "123456";
        public static final String DEFAULT_AVATAR = "/static/images/default-avatar.png";
        public static final int USERNAME_MIN_LENGTH = 3;
        public static final int USERNAME_MAX_LENGTH = 20;
        public static final int PASSWORD_MIN_LENGTH = 6;
        public static final int PASSWORD_MAX_LENGTH = 20;
        public static final int NICKNAME_MAX_LENGTH = 50;
        public static final int EMAIL_MAX_LENGTH = 100;
        public static final int PHONE_LENGTH = 11;
    }

    /**
     * 角色相关常量
     */
    public static final class Role {
        public static final String ADMIN = "admin";
        public static final String SHOP_OWNER = "shop_owner";
        public static final String CUSTOMER = "customer";
        public static final String GUEST = "guest";
    }

    /**
     * 权限相关常量
     */
    public static final class Permission {
        public static final String USER_MANAGE = "user:manage";
        public static final String SHOP_MANAGE = "shop:manage";
        public static final String PRODUCT_MANAGE = "product:manage";
        public static final String ORDER_MANAGE = "order:manage";
        public static final String SYSTEM_MANAGE = "system:manage";
    }

    /**
     * 状态相关常量
     */
    public static final class Status {
        public static final int ENABLED = 1;
        public static final int DISABLED = 0;
        public static final int DELETED = -1;
        
        public static final String ACTIVE = "ACTIVE";
        public static final String INACTIVE = "INACTIVE";
        public static final String PENDING = "PENDING";
        public static final String APPROVED = "APPROVED";
        public static final String REJECTED = "REJECTED";
    }

    /**
     * 店铺相关常量
     */
    public static final class Shop {
        public static final int NAME_MIN_LENGTH = 2;
        public static final int NAME_MAX_LENGTH = 50;
        public static final int DESCRIPTION_MAX_LENGTH = 500;
        public static final String DEFAULT_LOGO = "/static/images/default-shop-logo.png";
        
        // 店铺状态
        public static final String STATUS_PENDING = "PENDING";     // 待审核
        public static final String STATUS_APPROVED = "APPROVED";   // 已通过
        public static final String STATUS_REJECTED = "REJECTED";   // 已拒绝
        public static final String STATUS_SUSPENDED = "SUSPENDED"; // 已暂停
        public static final String STATUS_CLOSED = "CLOSED";       // 已关闭
    }

    /**
     * 商品相关常量
     */
    public static final class Product {
        public static final int NAME_MIN_LENGTH = 2;
        public static final int NAME_MAX_LENGTH = 100;
        public static final int DESCRIPTION_MAX_LENGTH = 2000;
        public static final String DEFAULT_IMAGE = "/static/images/default-product.png";
        public static final int MAX_IMAGES = 10;
        
        // 商品状态
        public static final String STATUS_DRAFT = "DRAFT";         // 草稿
        public static final String STATUS_ON_SALE = "ON_SALE";     // 在售
        public static final String STATUS_OFF_SALE = "OFF_SALE";   // 下架
        public static final String STATUS_OUT_OF_STOCK = "OUT_OF_STOCK"; // 缺货
    }

    /**
     * 订单相关常量
     */
    public static final class Order {
        // 订单状态
        public static final String STATUS_PENDING = "PENDING";           // 待付款
        public static final String STATUS_PAID = "PAID";                 // 已付款
        public static final String STATUS_SHIPPED = "SHIPPED";           // 已发货
        public static final String STATUS_DELIVERED = "DELIVERED";       // 已送达
        public static final String STATUS_COMPLETED = "COMPLETED";       // 已完成
        public static final String STATUS_CANCELLED = "CANCELLED";       // 已取消
        public static final String STATUS_REFUNDED = "REFUNDED";         // 已退款
        
        // 订单超时时间（分钟）
        public static final int PAYMENT_TIMEOUT = 30;    // 支付超时
        public static final int DELIVERY_TIMEOUT = 7200; // 发货超时（5天）
        public static final int RECEIVE_TIMEOUT = 10080; // 收货超时（7天）
    }

    /**
     * 支付相关常量
     */
    public static final class Payment {
        // 支付方式
        public static final String METHOD_ALIPAY = "ALIPAY";
        public static final String METHOD_WECHAT = "WECHAT";
        public static final String METHOD_BALANCE = "BALANCE";
        
        // 支付状态
        public static final String STATUS_PENDING = "PENDING";     // 待支付
        public static final String STATUS_SUCCESS = "SUCCESS";     // 支付成功
        public static final String STATUS_FAILED = "FAILED";       // 支付失败
        public static final String STATUS_CANCELLED = "CANCELLED"; // 支付取消
        public static final String STATUS_REFUNDED = "REFUNDED";   // 已退款
    }

    /**
     * 文件相关常量
     */
    public static final class File {
        public static final String UPLOAD_PATH = "/uploads";
        public static final String TEMP_PATH = "/temp";
        public static final long MAX_SIZE = 10 * 1024 * 1024; // 10MB
        
        // 允许的图片类型
        public static final String[] IMAGE_TYPES = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};
        
        // 允许的文档类型
        public static final String[] DOCUMENT_TYPES = {"pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt"};
    }

    /**
     * 正则表达式常量
     */
    public static final class Regex {
        public static final String EMAIL = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";
        public static final String PHONE = "^1[3-9]\\d{9}$";
        public static final String USERNAME = "^[a-zA-Z0-9_]{3,20}$";
        public static final String PASSWORD = "^(?=.*[a-zA-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{6,20}$";
        public static final String ID_CARD = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
    }

    /**
     * 日期时间格式常量
     */
    public static final class DateTime {
        public static final String DATE_FORMAT = "yyyy-MM-dd";
        public static final String TIME_FORMAT = "HH:mm:ss";
        public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
        public static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss.SSS";
        public static final String DATE_TIME_FORMAT_SHORT = "yyyyMMddHHmmss";
        public static final String DATE_FORMAT_SHORT = "yyyyMMdd";
    }

    /**
     * HTTP相关常量
     */
    public static final class Http {
        public static final String CONTENT_TYPE_JSON = "application/json";
        public static final String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded";
        public static final String CONTENT_TYPE_MULTIPART = "multipart/form-data";
        public static final String CHARSET_UTF8 = "charset=UTF-8";
        
        public static final String HEADER_AUTHORIZATION = "Authorization";
        public static final String HEADER_CONTENT_TYPE = "Content-Type";
        public static final String HEADER_USER_AGENT = "User-Agent";
        public static final String HEADER_X_FORWARDED_FOR = "X-Forwarded-For";
        public static final String HEADER_X_REAL_IP = "X-Real-IP";
    }
}

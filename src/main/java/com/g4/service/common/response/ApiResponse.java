package com.g4.service.common.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.g4.service.common.enums.ResponseCode;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一API响应体
 * @param <T> 响应数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ApiResponse<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 请求追踪ID（可选）
     */
    private String traceId;
    
    public ApiResponse(Integer code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }
    
    public ApiResponse(ResponseCode responseCode, T data) {
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }
    
    // ========== 成功响应 ==========
    
    /**
     * 成功响应（无数据）
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(ResponseCode.SUCCESS, null);
    }
    
    /**
     * 成功响应（带数据）
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(ResponseCode.SUCCESS, data);
    }
    
    /**
     * 成功响应（自定义消息）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(ResponseCode.SUCCESS.getCode(), message, data);
    }
    
    // ========== 失败响应 ==========
    
    /**
     * 失败响应（使用枚举）
     */
    public static <T> ApiResponse<T> error(ResponseCode responseCode) {
        return new ApiResponse<>(responseCode, null);
    }
    
    /**
     * 失败响应（使用枚举，带数据）
     */
    public static <T> ApiResponse<T> error(ResponseCode responseCode, T data) {
        return new ApiResponse<>(responseCode, data);
    }
    
    /**
     * 失败响应（自定义消息）
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }
    
    /**
     * 失败响应（自定义消息，带数据）
     */
    public static <T> ApiResponse<T> error(Integer code, String message, T data) {
        return new ApiResponse<>(code, message, data);
    }
    
    // ========== 便捷方法 ==========
    
    /**
     * 参数错误
     */
    public static <T> ApiResponse<T> paramError(String message) {
        return new ApiResponse<>(ResponseCode.PARAM_ERROR.getCode(), message, null);
    }
    
    /**
     * 业务错误
     */
    public static <T> ApiResponse<T> businessError(String message) {
        return new ApiResponse<>(ResponseCode.BUSINESS_ERROR.getCode(), message, null);
    }
    
    /**
     * 未认证
     */
    public static <T> ApiResponse<T> unauthorized() {
        return new ApiResponse<>(ResponseCode.UNAUTHORIZED, null);
    }
    
    /**
     * 权限不足
     */
    public static <T> ApiResponse<T> forbidden() {
        return new ApiResponse<>(ResponseCode.FORBIDDEN, null);
    }
    
    /**
     * 服务器内部错误
     */
    public static <T> ApiResponse<T> internalError() {
        return new ApiResponse<>(ResponseCode.INTERNAL_ERROR, null);
    }
    
    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResponseCode.SUCCESS.getCode().equals(this.code);
    }
    
    /**
     * 设置追踪ID
     */
    public ApiResponse<T> traceId(String traceId) {
        this.traceId = traceId;
        return this;
    }
}

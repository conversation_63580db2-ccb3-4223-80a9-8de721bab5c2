package com.g4.service.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应码枚举
 * 定义系统中所有的响应码
 */
@Getter
@AllArgsConstructor
public enum ResponseCode {

    // 成功
    SUCCESS("200", "操作成功"),

    // 客户端错误 4xx
    BAD_REQUEST("400", "请求参数错误"),
    UNAUTHORIZED("401", "未授权"),
    FORBIDDEN("403", "禁止访问"),
    NOT_FOUND("404", "资源不存在"),
    METHOD_NOT_ALLOWED("405", "请求方法不允许"),
    CONFLICT("409", "资源冲突"),
    UNSUPPORTED_MEDIA_TYPE("415", "不支持的媒体类型"),
    PARAM_ERROR("422", "参数校验失败"),

    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR("500", "服务器内部错误"),
    NOT_IMPLEMENTED("501", "功能未实现"),
    BAD_GATEWAY("502", "网关错误"),
    SERVICE_UNAVAILABLE("503", "服务不可用"),
    GATEWAY_TIMEOUT("504", "网关超时"),

    // 业务错误 1xxx
    BUSINESS_ERROR("1000", "业务处理失败"),
    USER_NOT_FOUND("1001", "用户不存在"),
    USER_ALREADY_EXISTS("1002", "用户已存在"),
    USER_DISABLED("1003", "用户已被禁用"),
    PASSWORD_ERROR("1004", "密码错误"),
    OLD_PASSWORD_ERROR("1005", "原密码错误"),
    USER_NOT_LOGIN("1006", "用户未登录"),
    TOKEN_EXPIRED("1007", "令牌已过期"),
    TOKEN_INVALID("1008", "令牌无效"),
    PERMISSION_DENIED("1009", "权限不足"),
    ROLE_NOT_FOUND("1010", "角色不存在"),

    // 店铺相关错误 2xxx
    SHOP_NOT_FOUND("2001", "店铺不存在"),
    SHOP_ALREADY_EXISTS("2002", "店铺已存在"),
    SHOP_DISABLED("2003", "店铺已被禁用"),
    SHOP_NOT_OWNER("2004", "非店铺所有者"),
    SHOP_STATUS_ERROR("2005", "店铺状态错误"),

    // 商品相关错误 3xxx
    PRODUCT_NOT_FOUND("3001", "商品不存在"),
    PRODUCT_ALREADY_EXISTS("3002", "商品已存在"),
    PRODUCT_DISABLED("3003", "商品已下架"),
    PRODUCT_STOCK_INSUFFICIENT("3004", "商品库存不足"),
    PRODUCT_CATEGORY_NOT_FOUND("3005", "商品分类不存在"),
    PRODUCT_PRICE_ERROR("3006", "商品价格错误"),

    // 订单相关错误 4xxx
    ORDER_NOT_FOUND("4001", "订单不存在"),
    ORDER_STATUS_ERROR("4002", "订单状态错误"),
    ORDER_CANNOT_CANCEL("4003", "订单无法取消"),
    ORDER_CANNOT_REFUND("4004", "订单无法退款"),
    ORDER_AMOUNT_ERROR("4005", "订单金额错误"),

    // 支付相关错误 5xxx
    PAYMENT_FAILED("5001", "支付失败"),
    PAYMENT_TIMEOUT("5002", "支付超时"),
    PAYMENT_CANCELLED("5003", "支付已取消"),
    PAYMENT_AMOUNT_ERROR("5004", "支付金额错误"),
    PAYMENT_METHOD_NOT_SUPPORTED("5005", "不支持的支付方式"),

    // 文件相关错误 6xxx
    FILE_NOT_FOUND("6001", "文件不存在"),
    FILE_UPLOAD_FAILED("6002", "文件上传失败"),
    FILE_DELETE_FAILED("6003", "文件删除失败"),
    FILE_SIZE_EXCEEDED("6004", "文件大小超限"),
    FILE_TYPE_NOT_SUPPORTED("6005", "不支持的文件类型"),

    // 数据库相关错误 7xxx
    DATABASE_ERROR("7001", "数据库操作失败"),
    DATA_NOT_FOUND("7002", "数据不存在"),
    DATA_ALREADY_EXISTS("7003", "数据已存在"),
    DATA_INTEGRITY_VIOLATION("7004", "数据完整性违反"),
    OPTIMISTIC_LOCK_ERROR("7005", "乐观锁冲突"),

    // 缓存相关错误 8xxx
    CACHE_ERROR("8001", "缓存操作失败"),
    CACHE_KEY_NOT_FOUND("8002", "缓存键不存在"),
    CACHE_EXPIRED("8003", "缓存已过期"),

    // 第三方服务错误 9xxx
    THIRD_PARTY_SERVICE_ERROR("9001", "第三方服务错误"),
    SMS_SEND_FAILED("9002", "短信发送失败"),
    EMAIL_SEND_FAILED("9003", "邮件发送失败"),
    WECHAT_API_ERROR("9004", "微信接口错误"),
    ALIPAY_API_ERROR("9005", "支付宝接口错误");

    /**
     * 响应码
     */
    private final String code;

    /**
     * 响应消息
     */
    private final String message;
}

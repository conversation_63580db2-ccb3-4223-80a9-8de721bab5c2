package com.g4.service.common;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一响应结果类
 * 
 * @param <T> 数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     */
    private String code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 响应时间
     */
    private LocalDateTime timestamp;

    /**
     * 请求ID（用于链路追踪）
     */
    private String requestId;

    /**
     * 构造函数
     */
    public ResponseResult(String code, String message) {
        this.code = code;
        this.message = message;
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 构造函数
     */
    public ResponseResult(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 构造函数
     */
    public ResponseResult(ResponseCode responseCode) {
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 构造函数
     */
    public ResponseResult(ResponseCode responseCode, T data) {
        this.code = responseCode.getCode();
        this.message = responseCode.getMessage();
        this.data = data;
        this.timestamp = LocalDateTime.now();
    }

    /**
     * 成功响应
     */
    public static <T> ResponseResult<T> success() {
        return new ResponseResult<>(ResponseCode.SUCCESS);
    }

    /**
     * 成功响应
     */
    public static <T> ResponseResult<T> success(T data) {
        return new ResponseResult<>(ResponseCode.SUCCESS, data);
    }

    /**
     * 成功响应
     */
    public static <T> ResponseResult<T> success(String message) {
        return new ResponseResult<>(ResponseCode.SUCCESS.getCode(), message);
    }

    /**
     * 成功响应
     */
    public static <T> ResponseResult<T> success(String message, T data) {
        return new ResponseResult<>(ResponseCode.SUCCESS.getCode(), message, data);
    }

    /**
     * 失败响应
     */
    public static <T> ResponseResult<T> error() {
        return new ResponseResult<>(ResponseCode.INTERNAL_SERVER_ERROR);
    }

    /**
     * 失败响应
     */
    public static <T> ResponseResult<T> error(String message) {
        return new ResponseResult<>(ResponseCode.INTERNAL_SERVER_ERROR.getCode(), message);
    }

    /**
     * 失败响应
     */
    public static <T> ResponseResult<T> error(String code, String message) {
        return new ResponseResult<>(code, message);
    }

    /**
     * 失败响应
     */
    public static <T> ResponseResult<T> error(ResponseCode responseCode) {
        return new ResponseResult<>(responseCode);
    }

    /**
     * 失败响应
     */
    public static <T> ResponseResult<T> error(ResponseCode responseCode, String message) {
        return new ResponseResult<>(responseCode.getCode(), message);
    }

    /**
     * 失败响应
     */
    public static <T> ResponseResult<T> error(ResponseCode responseCode, T data) {
        return new ResponseResult<>(responseCode, data);
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return ResponseCode.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 判断是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }

    /**
     * 设置请求ID
     */
    public ResponseResult<T> requestId(String requestId) {
        this.requestId = requestId;
        return this;
    }

    /**
     * 创建分页响应
     */
    public static <T> ResponseResult<PageResult<T>> page(PageResult<T> pageResult) {
        return success(pageResult);
    }

    /**
     * 创建分页响应
     */
    public static <T> ResponseResult<PageResult<T>> page(java.util.List<T> list, long total, int pageNum, int pageSize) {
        PageResult<T> pageResult = new PageResult<>(list, total, pageNum, pageSize);
        return success(pageResult);
    }
}

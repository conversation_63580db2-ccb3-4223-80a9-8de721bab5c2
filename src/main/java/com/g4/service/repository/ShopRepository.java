package com.g4.service.repository;

import com.g4.service.entity.Shop;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 店铺Repository接口
 * 提供店铺数据访问方法
 */
@Repository
public interface ShopRepository extends JpaRepository<Shop, Long>, JpaSpecificationExecutor<Shop> {

    /**
     * 根据店铺编码查找店铺
     */
    Optional<Shop> findByShopCode(String shopCode);

    /**
     * 根据店铺名称查找店铺
     */
    Optional<Shop> findByShopName(String shopName);

    /**
     * 检查店铺编码是否存在
     */
    boolean existsByShopCode(String shopCode);

    /**
     * 检查店铺名称是否存在
     */
    boolean existsByShopName(String shopName);

    /**
     * 根据店主ID查找店铺
     */
    List<Shop> findByOwnerId(Long ownerId);

    /**
     * 根据店主ID分页查询店铺
     */
    Page<Shop> findByOwnerId(Long ownerId, Pageable pageable);

    /**
     * 根据店铺状态查找店铺
     */
    List<Shop> findByStatus(Integer status);

    /**
     * 根据店铺状态分页查询店铺
     */
    Page<Shop> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据店铺类型查找店铺
     */
    List<Shop> findByShopType(Integer shopType);

    /**
     * 根据店铺类型分页查询店铺
     */
    Page<Shop> findByShopType(Integer shopType, Pageable pageable);

    /**
     * 根据店主ID和状态查找店铺
     */
    List<Shop> findByOwnerIdAndStatus(Long ownerId, Integer status);

    /**
     * 根据关键词搜索店铺（店铺编码、店铺名称、描述）
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false AND " +
           "(s.shopCode LIKE %:keyword% OR s.shopName LIKE %:keyword% OR s.description LIKE %:keyword%)")
    Page<Shop> searchShops(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找所有正常营业的店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false AND s.status = 0")
    List<Shop> findAllOpen();

    /**
     * 分页查找所有正常营业的店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false AND s.status = 0")
    Page<Shop> findAllOpen(Pageable pageable);

    /**
     * 查找所有未删除的店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false")
    List<Shop> findAllActive();

    /**
     * 分页查找所有未删除的店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false")
    Page<Shop> findAllActive(Pageable pageable);

    /**
     * 根据创建时间范围查询店铺
     */
    List<Shop> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据评分范围查询店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false AND s.rating BETWEEN :minRating AND :maxRating")
    List<Shop> findByRatingBetween(@Param("minRating") BigDecimal minRating, @Param("maxRating") BigDecimal maxRating);

    /**
     * 根据地理位置查找附近的店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false AND s.status = 0 AND " +
           "s.longitude IS NOT NULL AND s.latitude IS NOT NULL AND " +
           "(6371 * acos(cos(radians(:latitude)) * cos(radians(s.latitude)) * " +
           "cos(radians(s.longitude) - radians(:longitude)) + sin(radians(:latitude)) * " +
           "sin(radians(s.latitude)))) <= :distance")
    List<Shop> findNearbyShops(@Param("latitude") BigDecimal latitude, 
                              @Param("longitude") BigDecimal longitude, 
                              @Param("distance") Double distance);

    /**
     * 统计店铺总数
     */
    @Query("SELECT COUNT(s) FROM Shop s WHERE s.deleted = false")
    long countActiveShops();

    /**
     * 根据状态统计店铺数量
     */
    @Query("SELECT COUNT(s) FROM Shop s WHERE s.deleted = false AND s.status = :status")
    long countByStatus(@Param("status") Integer status);

    /**
     * 根据店铺类型统计店铺数量
     */
    @Query("SELECT COUNT(s) FROM Shop s WHERE s.deleted = false AND s.shopType = :shopType")
    long countByShopType(@Param("shopType") Integer shopType);

    /**
     * 根据店主ID统计店铺数量
     */
    @Query("SELECT COUNT(s) FROM Shop s WHERE s.deleted = false AND s.ownerId = :ownerId")
    long countByOwnerId(@Param("ownerId") Long ownerId);

    /**
     * 查找评分最高的店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false AND s.status = 0 ORDER BY s.rating DESC")
    List<Shop> findTopRatedShops(Pageable pageable);

    /**
     * 查找销售额最高的店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false AND s.status = 0 ORDER BY s.totalSales DESC")
    List<Shop> findTopSalesShops(Pageable pageable);

    /**
     * 查找商品数量最多的店铺
     */
    @Query("SELECT s FROM Shop s WHERE s.deleted = false AND s.status = 0 ORDER BY s.productCount DESC")
    List<Shop> findShopsWithMostProducts(Pageable pageable);

    /**
     * 更新店铺商品数量
     */
    @Modifying
    @Query("UPDATE Shop s SET s.productCount = :productCount WHERE s.id = :shopId")
    int updateProductCount(@Param("shopId") Long shopId, @Param("productCount") Integer productCount);

    /**
     * 增加店铺商品数量
     */
    @Modifying
    @Query("UPDATE Shop s SET s.productCount = s.productCount + :increment WHERE s.id = :shopId")
    int increaseProductCount(@Param("shopId") Long shopId, @Param("increment") Integer increment);

    /**
     * 减少店铺商品数量
     */
    @Modifying
    @Query("UPDATE Shop s SET s.productCount = GREATEST(0, s.productCount - :decrement) WHERE s.id = :shopId")
    int decreaseProductCount(@Param("shopId") Long shopId, @Param("decrement") Integer decrement);

    /**
     * 更新店铺销售总额
     */
    @Modifying
    @Query("UPDATE Shop s SET s.totalSales = :totalSales WHERE s.id = :shopId")
    int updateTotalSales(@Param("shopId") Long shopId, @Param("totalSales") BigDecimal totalSales);

    /**
     * 增加店铺销售总额
     */
    @Modifying
    @Query("UPDATE Shop s SET s.totalSales = s.totalSales + :amount WHERE s.id = :shopId")
    int addSales(@Param("shopId") Long shopId, @Param("amount") BigDecimal amount);

    /**
     * 更新店铺评分和评价数量
     */
    @Modifying
    @Query("UPDATE Shop s SET s.rating = :rating, s.reviewCount = :reviewCount WHERE s.id = :shopId")
    int updateRatingAndReviewCount(@Param("shopId") Long shopId, 
                                  @Param("rating") BigDecimal rating, 
                                  @Param("reviewCount") Integer reviewCount);

    /**
     * 批量更新店铺状态
     */
    @Modifying
    @Query("UPDATE Shop s SET s.status = :status WHERE s.id IN :shopIds")
    int updateStatusBatch(@Param("shopIds") List<Long> shopIds, @Param("status") Integer status);

    /**
     * 软删除店铺
     */
    @Modifying
    @Query("UPDATE Shop s SET s.deleted = true WHERE s.id = :shopId")
    int softDeleteShop(@Param("shopId") Long shopId);

    /**
     * 批量软删除店铺
     */
    @Modifying
    @Query("UPDATE Shop s SET s.deleted = true WHERE s.id IN :shopIds")
    int softDeleteShopsBatch(@Param("shopIds") List<Long> shopIds);
}

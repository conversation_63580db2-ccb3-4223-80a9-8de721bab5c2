package com.g4.service.repository;

import com.g4.service.entity.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户角色关联Repository接口
 * 提供用户角色关联数据访问方法
 */
@Repository
public interface UserRoleRepository extends JpaRepository<UserRole, Long> {

    /**
     * 根据用户ID查找用户角色关联
     */
    List<UserRole> findByUserId(Long userId);

    /**
     * 根据角色ID查找用户角色关联
     */
    List<UserRole> findByRoleId(Long roleId);

    /**
     * 根据用户ID和角色ID查找用户角色关联
     */
    Optional<UserRole> findByUserIdAndRoleId(Long userId, Long roleId);

    /**
     * 检查用户是否拥有指定角色
     */
    boolean existsByUserIdAndRoleId(Long userId, Long roleId);

    /**
     * 根据用户ID删除用户角色关联
     */
    @Modifying
    @Query("DELETE FROM UserRole ur WHERE ur.userId = :userId")
    int deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID删除用户角色关联
     */
    @Modifying
    @Query("DELETE FROM UserRole ur WHERE ur.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID和角色ID删除用户角色关联
     */
    @Modifying
    @Query("DELETE FROM UserRole ur WHERE ur.userId = :userId AND ur.roleId = :roleId")
    int deleteByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);

    /**
     * 批量删除用户角色关联
     */
    @Modifying
    @Query("DELETE FROM UserRole ur WHERE ur.userId = :userId AND ur.roleId IN :roleIds")
    int deleteByUserIdAndRoleIds(@Param("userId") Long userId, @Param("roleIds") List<Long> roleIds);

    /**
     * 根据用户ID列表查找用户角色关联
     */
    @Query("SELECT ur FROM UserRole ur WHERE ur.userId IN :userIds")
    List<UserRole> findByUserIds(@Param("userIds") List<Long> userIds);

    /**
     * 根据角色ID列表查找用户角色关联
     */
    @Query("SELECT ur FROM UserRole ur WHERE ur.roleId IN :roleIds")
    List<UserRole> findByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 统计用户数量（根据角色ID）
     */
    @Query("SELECT COUNT(ur) FROM UserRole ur WHERE ur.roleId = :roleId")
    long countUsersByRoleId(@Param("roleId") Long roleId);

    /**
     * 统计角色数量（根据用户ID）
     */
    @Query("SELECT COUNT(ur) FROM UserRole ur WHERE ur.userId = :userId")
    long countRolesByUserId(@Param("userId") Long userId);

    /**
     * 查找拥有特定角色的用户ID列表
     */
    @Query("SELECT ur.userId FROM UserRole ur WHERE ur.roleId = :roleId")
    List<Long> findUserIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 查找用户拥有的角色ID列表
     */
    @Query("SELECT ur.roleId FROM UserRole ur WHERE ur.userId = :userId")
    List<Long> findRoleIdsByUserId(@Param("userId") Long userId);

    /**
     * 检查用户是否拥有指定角色编码
     */
    @Query("SELECT COUNT(ur) > 0 FROM UserRole ur JOIN ur.role r WHERE ur.userId = :userId AND r.roleCode = :roleCode")
    boolean existsByUserIdAndRoleCode(@Param("userId") Long userId, @Param("roleCode") String roleCode);

    /**
     * 查找拥有特定角色编码的用户ID列表
     */
    @Query("SELECT ur.userId FROM UserRole ur JOIN ur.role r WHERE r.roleCode = :roleCode")
    List<Long> findUserIdsByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 批量插入用户角色关联
     */
    @Modifying
    @Query(value = "INSERT INTO sys_user_role (user_id, role_id, create_time, update_time, deleted, version) " +
                   "VALUES (:userId, :roleId, NOW(), NOW(), false, 0)", nativeQuery = true)
    int insertUserRole(@Param("userId") Long userId, @Param("roleId") Long roleId);
}

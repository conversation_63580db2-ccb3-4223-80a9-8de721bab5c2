package com.g4.service.repository;

import com.g4.service.entity.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 角色Repository接口
 * 提供角色数据访问方法
 */
@Repository
public interface RoleRepository extends JpaRepository<Role, Long>, JpaSpecificationExecutor<Role> {

    /**
     * 根据角色编码查找角色
     */
    Optional<Role> findByRoleCode(String roleCode);

    /**
     * 根据角色名称查找角色
     */
    Optional<Role> findByRoleName(String roleName);

    /**
     * 检查角色编码是否存在
     */
    boolean existsByRoleCode(String roleCode);

    /**
     * 检查角色名称是否存在
     */
    boolean existsByRoleName(String roleName);

    /**
     * 根据状态查找角色
     */
    List<Role> findByStatus(Integer status);

    /**
     * 根据状态分页查询角色
     */
    Page<Role> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据关键词搜索角色（角色编码、角色名称、描述）
     */
    @Query("SELECT r FROM Role r WHERE r.deleted = false AND " +
           "(r.roleCode LIKE %:keyword% OR r.roleName LIKE %:keyword% OR r.description LIKE %:keyword%)")
    Page<Role> searchRoles(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找所有启用的角色
     */
    @Query("SELECT r FROM Role r WHERE r.deleted = false AND r.status = 0 ORDER BY r.sort ASC")
    List<Role> findAllEnabled();

    /**
     * 查找所有未删除的角色，按排序号排序
     */
    @Query("SELECT r FROM Role r WHERE r.deleted = false ORDER BY r.sort ASC")
    List<Role> findAllActiveOrderBySort();

    /**
     * 分页查找所有未删除的角色
     */
    @Query("SELECT r FROM Role r WHERE r.deleted = false")
    Page<Role> findAllActive(Pageable pageable);

    /**
     * 统计角色总数
     */
    @Query("SELECT COUNT(r) FROM Role r WHERE r.deleted = false")
    long countActiveRoles();

    /**
     * 根据状态统计角色数量
     */
    @Query("SELECT COUNT(r) FROM Role r WHERE r.deleted = false AND r.status = :status")
    long countByStatus(@Param("status") Integer status);

    /**
     * 根据用户ID查找角色
     */
    @Query("SELECT r FROM Role r JOIN r.userRoles ur WHERE r.deleted = false AND ur.userId = :userId")
    List<Role> findByUserId(@Param("userId") Long userId);

    /**
     * 根据权限ID查找角色
     */
    @Query("SELECT r FROM Role r JOIN r.rolePermissions rp WHERE r.deleted = false AND rp.permissionId = :permissionId")
    List<Role> findByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 查找拥有特定权限的角色
     */
    @Query("SELECT DISTINCT r FROM Role r JOIN r.rolePermissions rp JOIN rp.permission p " +
           "WHERE r.deleted = false AND p.permissionCode = :permissionCode")
    List<Role> findByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 查找系统内置角色
     */
    @Query("SELECT r FROM Role r WHERE r.deleted = false AND " +
           "r.roleCode IN ('SUPER_ADMIN', 'ADMIN', 'USER') ORDER BY r.sort ASC")
    List<Role> findSystemRoles();

    /**
     * 查找非系统内置角色
     */
    @Query("SELECT r FROM Role r WHERE r.deleted = false AND " +
           "r.roleCode NOT IN ('SUPER_ADMIN', 'ADMIN', 'USER') ORDER BY r.sort ASC")
    List<Role> findCustomRoles();

    /**
     * 批量更新角色状态
     */
    @Modifying
    @Query("UPDATE Role r SET r.status = :status WHERE r.id IN :roleIds")
    int updateStatusBatch(@Param("roleIds") List<Long> roleIds, @Param("status") Integer status);

    /**
     * 软删除角色
     */
    @Modifying
    @Query("UPDATE Role r SET r.deleted = true WHERE r.id = :roleId")
    int softDeleteRole(@Param("roleId") Long roleId);

    /**
     * 批量软删除角色
     */
    @Modifying
    @Query("UPDATE Role r SET r.deleted = true WHERE r.id IN :roleIds")
    int softDeleteRolesBatch(@Param("roleIds") List<Long> roleIds);

    /**
     * 更新角色排序
     */
    @Modifying
    @Query("UPDATE Role r SET r.sort = :sort WHERE r.id = :roleId")
    int updateSort(@Param("roleId") Long roleId, @Param("sort") Integer sort);

    /**
     * 获取最大排序号
     */
    @Query("SELECT COALESCE(MAX(r.sort), 0) FROM Role r WHERE r.deleted = false")
    Integer getMaxSort();
}

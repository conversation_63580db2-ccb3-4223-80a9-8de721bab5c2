package com.g4.service.repository;

import com.g4.service.entity.Category;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 商品分类Repository接口
 * 提供商品分类数据访问方法
 */
@Repository
public interface CategoryRepository extends JpaRepository<Category, Long>, JpaSpecificationExecutor<Category> {

    /**
     * 根据分类编码查找分类
     */
    Optional<Category> findByCategoryCode(String categoryCode);

    /**
     * 根据分类名称查找分类
     */
    Optional<Category> findByCategoryName(String categoryName);

    /**
     * 检查分类编码是否存在
     */
    boolean existsByCategoryCode(String categoryCode);

    /**
     * 检查分类名称是否存在
     */
    boolean existsByCategoryName(String categoryName);

    /**
     * 根据父分类ID查找子分类
     */
    List<Category> findByParentId(Long parentId);

    /**
     * 根据父分类ID和状态查找子分类
     */
    List<Category> findByParentIdAndStatus(Long parentId, Integer status);

    /**
     * 根据分类层级查找分类
     */
    List<Category> findByLevel(Integer level);

    /**
     * 根据分类层级和状态查找分类
     */
    List<Category> findByLevelAndStatus(Integer level, Integer status);

    /**
     * 根据状态查找分类
     */
    List<Category> findByStatus(Integer status);

    /**
     * 根据状态分页查询分类
     */
    Page<Category> findByStatus(Integer status, Pageable pageable);

    /**
     * 查找根分类（父分类ID为空或0）
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false AND (c.parentId IS NULL OR c.parentId = 0) ORDER BY c.sort ASC")
    List<Category> findRootCategories();

    /**
     * 查找启用的根分类
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false AND c.status = 0 AND " +
           "(c.parentId IS NULL OR c.parentId = 0) ORDER BY c.sort ASC")
    List<Category> findEnabledRootCategories();

    /**
     * 查找所有启用的分类
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false AND c.status = 0 ORDER BY c.sort ASC")
    List<Category> findAllEnabled();

    /**
     * 查找所有未删除的分类，按排序号排序
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false ORDER BY c.sort ASC")
    List<Category> findAllActiveOrderBySort();

    /**
     * 分页查找所有未删除的分类
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false")
    Page<Category> findAllActive(Pageable pageable);

    /**
     * 根据关键词搜索分类（分类编码、分类名称、描述）
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false AND " +
           "(c.categoryCode LIKE %:keyword% OR c.categoryName LIKE %:keyword% OR c.description LIKE %:keyword%)")
    Page<Category> searchCategories(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据路径查找分类
     */
    List<Category> findByPath(String path);

    /**
     * 根据路径前缀查找分类（用于查找某个分类的所有子分类）
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false AND c.path LIKE :pathPrefix%")
    List<Category> findByPathStartingWith(@Param("pathPrefix") String pathPrefix);

    /**
     * 统计分类总数
     */
    @Query("SELECT COUNT(c) FROM Category c WHERE c.deleted = false")
    long countActiveCategories();

    /**
     * 根据状态统计分类数量
     */
    @Query("SELECT COUNT(c) FROM Category c WHERE c.deleted = false AND c.status = :status")
    long countByStatus(@Param("status") Integer status);

    /**
     * 根据层级统计分类数量
     */
    @Query("SELECT COUNT(c) FROM Category c WHERE c.deleted = false AND c.level = :level")
    long countByLevel(@Param("level") Integer level);

    /**
     * 根据父分类ID统计子分类数量
     */
    @Query("SELECT COUNT(c) FROM Category c WHERE c.deleted = false AND c.parentId = :parentId")
    long countByParentId(@Param("parentId") Long parentId);

    /**
     * 查找商品数量最多的分类
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false AND c.status = 0 ORDER BY c.productCount DESC")
    List<Category> findCategoriesWithMostProducts(Pageable pageable);

    /**
     * 查找叶子分类（没有子分类的分类）
     */
    @Query("SELECT c FROM Category c WHERE c.deleted = false AND c.status = 0 AND " +
           "c.id NOT IN (SELECT DISTINCT c2.parentId FROM Category c2 WHERE c2.deleted = false AND c2.parentId IS NOT NULL)")
    List<Category> findLeafCategories();

    /**
     * 更新分类商品数量
     */
    @Modifying
    @Query("UPDATE Category c SET c.productCount = :productCount WHERE c.id = :categoryId")
    int updateProductCount(@Param("categoryId") Long categoryId, @Param("productCount") Integer productCount);

    /**
     * 增加分类商品数量
     */
    @Modifying
    @Query("UPDATE Category c SET c.productCount = c.productCount + :increment WHERE c.id = :categoryId")
    int increaseProductCount(@Param("categoryId") Long categoryId, @Param("increment") Integer increment);

    /**
     * 减少分类商品数量
     */
    @Modifying
    @Query("UPDATE Category c SET c.productCount = GREATEST(0, c.productCount - :decrement) WHERE c.id = :categoryId")
    int decreaseProductCount(@Param("categoryId") Long categoryId, @Param("decrement") Integer decrement);

    /**
     * 批量更新分类状态
     */
    @Modifying
    @Query("UPDATE Category c SET c.status = :status WHERE c.id IN :categoryIds")
    int updateStatusBatch(@Param("categoryIds") List<Long> categoryIds, @Param("status") Integer status);

    /**
     * 更新分类排序
     */
    @Modifying
    @Query("UPDATE Category c SET c.sort = :sort WHERE c.id = :categoryId")
    int updateSort(@Param("categoryId") Long categoryId, @Param("sort") Integer sort);

    /**
     * 获取最大排序号
     */
    @Query("SELECT COALESCE(MAX(c.sort), 0) FROM Category c WHERE c.deleted = false")
    Integer getMaxSort();

    /**
     * 根据父分类ID获取最大排序号
     */
    @Query("SELECT COALESCE(MAX(c.sort), 0) FROM Category c WHERE c.deleted = false AND c.parentId = :parentId")
    Integer getMaxSortByParentId(@Param("parentId") Long parentId);

    /**
     * 软删除分类
     */
    @Modifying
    @Query("UPDATE Category c SET c.deleted = true WHERE c.id = :categoryId")
    int softDeleteCategory(@Param("categoryId") Long categoryId);

    /**
     * 批量软删除分类
     */
    @Modifying
    @Query("UPDATE Category c SET c.deleted = true WHERE c.id IN :categoryIds")
    int softDeleteCategoriesBatch(@Param("categoryIds") List<Long> categoryIds);

    /**
     * 软删除分类及其所有子分类
     */
    @Modifying
    @Query("UPDATE Category c SET c.deleted = true WHERE c.path LIKE :pathPrefix%")
    int softDeleteCategoryAndChildren(@Param("pathPrefix") String pathPrefix);
}

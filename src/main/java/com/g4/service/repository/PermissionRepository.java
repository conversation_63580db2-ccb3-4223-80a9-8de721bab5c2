package com.g4.service.repository;

import com.g4.service.entity.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 权限Repository接口
 * 提供权限数据访问方法
 */
@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long>, JpaSpecificationExecutor<Permission> {

    /**
     * 根据权限编码查找权限
     */
    Optional<Permission> findByPermissionCode(String permissionCode);

    /**
     * 根据权限名称查找权限
     */
    Optional<Permission> findByPermissionName(String permissionName);

    /**
     * 检查权限编码是否存在
     */
    boolean existsByPermissionCode(String permissionCode);

    /**
     * 检查权限名称是否存在
     */
    boolean existsByPermissionName(String permissionName);

    /**
     * 根据父权限ID查找子权限
     */
    List<Permission> findByParentId(Long parentId);

    /**
     * 根据权限类型查找权限
     */
    List<Permission> findByType(Integer type);

    /**
     * 根据状态查找权限
     */
    List<Permission> findByStatus(Integer status);

    /**
     * 根据父权限ID和状态查找权限
     */
    List<Permission> findByParentIdAndStatus(Long parentId, Integer status);

    /**
     * 根据权限类型和状态查找权限
     */
    List<Permission> findByTypeAndStatus(Integer type, Integer status);

    /**
     * 查找根权限（父权限ID为空或0）
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false AND (p.parentId IS NULL OR p.parentId = 0) ORDER BY p.sort ASC")
    List<Permission> findRootPermissions();

    /**
     * 查找启用的根权限
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false AND p.status = 0 AND " +
           "(p.parentId IS NULL OR p.parentId = 0) ORDER BY p.sort ASC")
    List<Permission> findEnabledRootPermissions();

    /**
     * 查找所有启用的权限
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false AND p.status = 0 ORDER BY p.sort ASC")
    List<Permission> findAllEnabled();

    /**
     * 查找所有未删除的权限，按排序号排序
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false ORDER BY p.sort ASC")
    List<Permission> findAllActiveOrderBySort();

    /**
     * 分页查找所有未删除的权限
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false")
    Page<Permission> findAllActive(Pageable pageable);

    /**
     * 根据关键词搜索权限（权限编码、权限名称、描述）
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false AND " +
           "(p.permissionCode LIKE %:keyword% OR p.permissionName LIKE %:keyword% OR p.description LIKE %:keyword%)")
    Page<Permission> searchPermissions(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 统计权限总数
     */
    @Query("SELECT COUNT(p) FROM Permission p WHERE p.deleted = false")
    long countActivePermissions();

    /**
     * 根据状态统计权限数量
     */
    @Query("SELECT COUNT(p) FROM Permission p WHERE p.deleted = false AND p.status = :status")
    long countByStatus(@Param("status") Integer status);

    /**
     * 根据权限类型统计权限数量
     */
    @Query("SELECT COUNT(p) FROM Permission p WHERE p.deleted = false AND p.type = :type")
    long countByType(@Param("type") Integer type);

    /**
     * 根据用户ID查找权限
     */
    @Query("SELECT DISTINCT p FROM Permission p JOIN p.rolePermissions rp JOIN rp.role r " +
           "JOIN r.userRoles ur WHERE p.deleted = false AND p.status = 0 AND ur.userId = :userId")
    List<Permission> findByUserId(@Param("userId") Long userId);

    /**
     * 根据角色ID查找权限
     */
    @Query("SELECT p FROM Permission p JOIN p.rolePermissions rp WHERE p.deleted = false AND rp.roleId = :roleId")
    List<Permission> findByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据角色编码查找权限
     */
    @Query("SELECT DISTINCT p FROM Permission p JOIN p.rolePermissions rp JOIN rp.role r " +
           "WHERE p.deleted = false AND p.status = 0 AND r.roleCode = :roleCode")
    List<Permission> findByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 查找菜单权限（类型为0或1）
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false AND p.status = 0 AND " +
           "p.type IN (0, 1) ORDER BY p.sort ASC")
    List<Permission> findMenuPermissions();

    /**
     * 查找按钮权限（类型为2）
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false AND p.status = 0 AND " +
           "p.type = 2 ORDER BY p.sort ASC")
    List<Permission> findButtonPermissions();

    /**
     * 根据路径查找权限
     */
    List<Permission> findByPath(String path);

    /**
     * 查找外链权限
     */
    @Query("SELECT p FROM Permission p WHERE p.deleted = false AND p.status = 0 AND " +
           "p.isExternal = true ORDER BY p.sort ASC")
    List<Permission> findExternalPermissions();

    /**
     * 批量更新权限状态
     */
    @Modifying
    @Query("UPDATE Permission p SET p.status = :status WHERE p.id IN :permissionIds")
    int updateStatusBatch(@Param("permissionIds") List<Long> permissionIds, @Param("status") Integer status);

    /**
     * 软删除权限
     */
    @Modifying
    @Query("UPDATE Permission p SET p.deleted = true WHERE p.id = :permissionId")
    int softDeletePermission(@Param("permissionId") Long permissionId);

    /**
     * 批量软删除权限
     */
    @Modifying
    @Query("UPDATE Permission p SET p.deleted = true WHERE p.id IN :permissionIds")
    int softDeletePermissionsBatch(@Param("permissionIds") List<Long> permissionIds);

    /**
     * 更新权限排序
     */
    @Modifying
    @Query("UPDATE Permission p SET p.sort = :sort WHERE p.id = :permissionId")
    int updateSort(@Param("permissionId") Long permissionId, @Param("sort") Integer sort);

    /**
     * 获取最大排序号
     */
    @Query("SELECT COALESCE(MAX(p.sort), 0) FROM Permission p WHERE p.deleted = false")
    Integer getMaxSort();

    /**
     * 根据父权限ID获取最大排序号
     */
    @Query("SELECT COALESCE(MAX(p.sort), 0) FROM Permission p WHERE p.deleted = false AND p.parentId = :parentId")
    Integer getMaxSortByParentId(@Param("parentId") Long parentId);
}

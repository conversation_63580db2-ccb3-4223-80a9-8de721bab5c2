package com.g4.service.repository;

import com.g4.service.entity.RolePermission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 角色权限关联Repository接口
 * 提供角色权限关联数据访问方法
 */
@Repository
public interface RolePermissionRepository extends JpaRepository<RolePermission, Long> {

    /**
     * 根据角色ID查找角色权限关联
     */
    List<RolePermission> findByRoleId(Long roleId);

    /**
     * 根据权限ID查找角色权限关联
     */
    List<RolePermission> findByPermissionId(Long permissionId);

    /**
     * 根据角色ID和权限ID查找角色权限关联
     */
    Optional<RolePermission> findByRoleIdAndPermissionId(Long roleId, Long permissionId);

    /**
     * 检查角色是否拥有指定权限
     */
    boolean existsByRoleIdAndPermissionId(Long roleId, Long permissionId);

    /**
     * 根据角色ID删除角色权限关联
     */
    @Modifying
    @Query("DELETE FROM RolePermission rp WHERE rp.roleId = :roleId")
    int deleteByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据权限ID删除角色权限关联
     */
    @Modifying
    @Query("DELETE FROM RolePermission rp WHERE rp.permissionId = :permissionId")
    int deleteByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 根据角色ID和权限ID删除角色权限关联
     */
    @Modifying
    @Query("DELETE FROM RolePermission rp WHERE rp.roleId = :roleId AND rp.permissionId = :permissionId")
    int deleteByRoleIdAndPermissionId(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);

    /**
     * 批量删除角色权限关联
     */
    @Modifying
    @Query("DELETE FROM RolePermission rp WHERE rp.roleId = :roleId AND rp.permissionId IN :permissionIds")
    int deleteByRoleIdAndPermissionIds(@Param("roleId") Long roleId, @Param("permissionIds") List<Long> permissionIds);

    /**
     * 根据角色ID列表查找角色权限关联
     */
    @Query("SELECT rp FROM RolePermission rp WHERE rp.roleId IN :roleIds")
    List<RolePermission> findByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 根据权限ID列表查找角色权限关联
     */
    @Query("SELECT rp FROM RolePermission rp WHERE rp.permissionId IN :permissionIds")
    List<RolePermission> findByPermissionIds(@Param("permissionIds") List<Long> permissionIds);

    /**
     * 统计角色数量（根据权限ID）
     */
    @Query("SELECT COUNT(rp) FROM RolePermission rp WHERE rp.permissionId = :permissionId")
    long countRolesByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 统计权限数量（根据角色ID）
     */
    @Query("SELECT COUNT(rp) FROM RolePermission rp WHERE rp.roleId = :roleId")
    long countPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 查找拥有特定权限的角色ID列表
     */
    @Query("SELECT rp.roleId FROM RolePermission rp WHERE rp.permissionId = :permissionId")
    List<Long> findRoleIdsByPermissionId(@Param("permissionId") Long permissionId);

    /**
     * 查找角色拥有的权限ID列表
     */
    @Query("SELECT rp.permissionId FROM RolePermission rp WHERE rp.roleId = :roleId")
    List<Long> findPermissionIdsByRoleId(@Param("roleId") Long roleId);

    /**
     * 检查角色是否拥有指定权限编码
     */
    @Query("SELECT COUNT(rp) > 0 FROM RolePermission rp JOIN rp.permission p WHERE rp.roleId = :roleId AND p.permissionCode = :permissionCode")
    boolean existsByRoleIdAndPermissionCode(@Param("roleId") Long roleId, @Param("permissionCode") String permissionCode);

    /**
     * 查找拥有特定权限编码的角色ID列表
     */
    @Query("SELECT rp.roleId FROM RolePermission rp JOIN rp.permission p WHERE p.permissionCode = :permissionCode")
    List<Long> findRoleIdsByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 根据角色编码查找权限ID列表
     */
    @Query("SELECT rp.permissionId FROM RolePermission rp JOIN rp.role r WHERE r.roleCode = :roleCode")
    List<Long> findPermissionIdsByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 批量插入角色权限关联
     */
    @Modifying
    @Query(value = "INSERT INTO sys_role_permission (role_id, permission_id, create_time, update_time, deleted, version) " +
                   "VALUES (:roleId, :permissionId, NOW(), NOW(), false, 0)", nativeQuery = true)
    int insertRolePermission(@Param("roleId") Long roleId, @Param("permissionId") Long permissionId);
}

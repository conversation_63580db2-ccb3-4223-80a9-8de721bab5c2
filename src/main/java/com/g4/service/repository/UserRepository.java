package com.g4.service.repository;

import com.g4.service.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户Repository接口
 * 提供用户数据访问方法
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据手机号查找用户
     */
    Optional<User> findByPhone(String phone);

    /**
     * 根据用户名或邮箱查找用户
     */
    Optional<User> findByUsernameOrEmail(String username, String email);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 检查手机号是否存在
     */
    boolean existsByPhone(String phone);

    /**
     * 根据用户状态查找用户
     */
    List<User> findByStatus(Integer status);

    /**
     * 根据用户类型查找用户
     */
    List<User> findByUserType(Integer userType);

    /**
     * 根据用户状态分页查询
     */
    Page<User> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据用户类型分页查询
     */
    Page<User> findByUserType(Integer userType, Pageable pageable);

    /**
     * 根据创建时间范围查询用户
     */
    List<User> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据最后登录时间范围查询用户
     */
    List<User> findByLastLoginTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 查找长时间未登录的用户
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginTime < :time OR u.lastLoginTime IS NULL")
    List<User> findInactiveUsers(@Param("time") LocalDateTime time);

    /**
     * 根据关键词搜索用户（用户名、昵称、真实姓名、邮箱）
     */
    @Query("SELECT u FROM User u WHERE u.deleted = false AND " +
           "(u.username LIKE %:keyword% OR u.nickname LIKE %:keyword% OR " +
           "u.realName LIKE %:keyword% OR u.email LIKE %:keyword%)")
    Page<User> searchUsers(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 统计用户总数
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.deleted = false")
    long countActiveUsers();

    /**
     * 根据状态统计用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.deleted = false AND u.status = :status")
    long countByStatus(@Param("status") Integer status);

    /**
     * 根据用户类型统计用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.deleted = false AND u.userType = :userType")
    long countByUserType(@Param("userType") Integer userType);

    /**
     * 更新用户最后登录信息
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginTime = :loginTime, u.lastLoginIp = :loginIp, " +
           "u.loginCount = u.loginCount + 1 WHERE u.id = :userId")
    int updateLoginInfo(@Param("userId") Long userId, 
                       @Param("loginTime") LocalDateTime loginTime, 
                       @Param("loginIp") String loginIp);

    /**
     * 批量更新用户状态
     */
    @Modifying
    @Query("UPDATE User u SET u.status = :status WHERE u.id IN :userIds")
    int updateStatusBatch(@Param("userIds") List<Long> userIds, @Param("status") Integer status);

    /**
     * 软删除用户
     */
    @Modifying
    @Query("UPDATE User u SET u.deleted = true WHERE u.id = :userId")
    int softDeleteUser(@Param("userId") Long userId);

    /**
     * 批量软删除用户
     */
    @Modifying
    @Query("UPDATE User u SET u.deleted = true WHERE u.id IN :userIds")
    int softDeleteUsersBatch(@Param("userIds") List<Long> userIds);

    /**
     * 查找所有未删除的用户
     */
    @Query("SELECT u FROM User u WHERE u.deleted = false")
    List<User> findAllActive();

    /**
     * 分页查找所有未删除的用户
     */
    @Query("SELECT u FROM User u WHERE u.deleted = false")
    Page<User> findAllActive(Pageable pageable);

    /**
     * 根据角色查找用户
     */
    @Query("SELECT DISTINCT u FROM User u JOIN u.userRoles ur JOIN ur.role r " +
           "WHERE u.deleted = false AND r.roleCode = :roleCode")
    List<User> findByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 查找拥有特定权限的用户
     */
    @Query("SELECT DISTINCT u FROM User u JOIN u.userRoles ur JOIN ur.role r " +
           "JOIN r.rolePermissions rp JOIN rp.permission p " +
           "WHERE u.deleted = false AND p.permissionCode = :permissionCode")
    List<User> findByPermissionCode(@Param("permissionCode") String permissionCode);
}

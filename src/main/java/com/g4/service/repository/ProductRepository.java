package com.g4.service.repository;

import com.g4.service.entity.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 商品Repository接口
 * 提供商品数据访问方法
 */
@Repository
public interface ProductRepository extends JpaRepository<Product, Long>, JpaSpecificationExecutor<Product> {

    /**
     * 根据商品编码查找商品
     */
    Optional<Product> findByProductCode(String productCode);

    /**
     * 根据商品名称查找商品
     */
    List<Product> findByProductName(String productName);

    /**
     * 检查商品编码是否存在
     */
    boolean existsByProductCode(String productCode);

    /**
     * 根据店铺ID查找商品
     */
    List<Product> findByShopId(Long shopId);

    /**
     * 根据店铺ID分页查询商品
     */
    Page<Product> findByShopId(Long shopId, Pageable pageable);

    /**
     * 根据分类ID查找商品
     */
    List<Product> findByCategoryId(Long categoryId);

    /**
     * 根据分类ID分页查询商品
     */
    Page<Product> findByCategoryId(Long categoryId, Pageable pageable);

    /**
     * 根据商品状态查找商品
     */
    List<Product> findByStatus(Integer status);

    /**
     * 根据商品状态分页查询商品
     */
    Page<Product> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据店铺ID和状态查找商品
     */
    List<Product> findByShopIdAndStatus(Long shopId, Integer status);

    /**
     * 根据店铺ID和状态分页查询商品
     */
    Page<Product> findByShopIdAndStatus(Long shopId, Integer status, Pageable pageable);

    /**
     * 根据分类ID和状态查找商品
     */
    List<Product> findByCategoryIdAndStatus(Long categoryId, Integer status);

    /**
     * 根据分类ID和状态分页查询商品
     */
    Page<Product> findByCategoryIdAndStatus(Long categoryId, Integer status, Pageable pageable);

    /**
     * 根据关键词搜索商品（商品编码、商品名称、简介、品牌）
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND " +
           "(p.productCode LIKE %:keyword% OR p.productName LIKE %:keyword% OR " +
           "p.summary LIKE %:keyword% OR p.brand LIKE %:keyword%)")
    Page<Product> searchProducts(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据价格范围查询商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.status = 0 AND " +
           "p.price BETWEEN :minPrice AND :maxPrice")
    Page<Product> findByPriceBetween(@Param("minPrice") BigDecimal minPrice, 
                                    @Param("maxPrice") BigDecimal maxPrice, 
                                    Pageable pageable);

    /**
     * 查找所有正常销售的商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.status = 0 AND p.stock > 0")
    List<Product> findAllOnSale();

    /**
     * 分页查找所有正常销售的商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.status = 0 AND p.stock > 0")
    Page<Product> findAllOnSale(Pageable pageable);

    /**
     * 查找所有未删除的商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false")
    List<Product> findAllActive();

    /**
     * 分页查找所有未删除的商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false")
    Page<Product> findAllActive(Pageable pageable);

    /**
     * 查找推荐商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.status = 0 AND " +
           "p.isRecommended = true ORDER BY p.sort ASC")
    List<Product> findRecommendedProducts(Pageable pageable);

    /**
     * 查找新品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.status = 0 AND " +
           "p.isNew = true ORDER BY p.createTime DESC")
    List<Product> findNewProducts(Pageable pageable);

    /**
     * 查找热销商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.status = 0 AND " +
           "p.isHot = true ORDER BY p.salesCount DESC")
    List<Product> findHotProducts(Pageable pageable);

    /**
     * 查找销量最高的商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.status = 0 ORDER BY p.salesCount DESC")
    List<Product> findTopSalesProducts(Pageable pageable);

    /**
     * 查找评分最高的商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.status = 0 ORDER BY p.rating DESC")
    List<Product> findTopRatedProducts(Pageable pageable);

    /**
     * 查找库存不足的商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.stock <= p.warningStock")
    List<Product> findLowStockProducts();

    /**
     * 查找售罄的商品
     */
    @Query("SELECT p FROM Product p WHERE p.deleted = false AND p.stock = 0")
    List<Product> findSoldOutProducts();

    /**
     * 根据创建时间范围查询商品
     */
    List<Product> findByCreateTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据上架时间范围查询商品
     */
    List<Product> findByPublishTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计商品总数
     */
    @Query("SELECT COUNT(p) FROM Product p WHERE p.deleted = false")
    long countActiveProducts();

    /**
     * 根据状态统计商品数量
     */
    @Query("SELECT COUNT(p) FROM Product p WHERE p.deleted = false AND p.status = :status")
    long countByStatus(@Param("status") Integer status);

    /**
     * 根据店铺ID统计商品数量
     */
    @Query("SELECT COUNT(p) FROM Product p WHERE p.deleted = false AND p.shopId = :shopId")
    long countByShopId(@Param("shopId") Long shopId);

    /**
     * 根据分类ID统计商品数量
     */
    @Query("SELECT COUNT(p) FROM Product p WHERE p.deleted = false AND p.categoryId = :categoryId")
    long countByCategoryId(@Param("categoryId") Long categoryId);

    /**
     * 根据店铺ID和状态统计商品数量
     */
    @Query("SELECT COUNT(p) FROM Product p WHERE p.deleted = false AND p.shopId = :shopId AND p.status = :status")
    long countByShopIdAndStatus(@Param("shopId") Long shopId, @Param("status") Integer status);

    /**
     * 更新商品库存
     */
    @Modifying
    @Query("UPDATE Product p SET p.stock = :stock WHERE p.id = :productId")
    int updateStock(@Param("productId") Long productId, @Param("stock") Integer stock);

    /**
     * 减少商品库存
     */
    @Modifying
    @Query("UPDATE Product p SET p.stock = GREATEST(0, p.stock - :decrement) WHERE p.id = :productId AND p.stock >= :decrement")
    int decreaseStock(@Param("productId") Long productId, @Param("decrement") Integer decrement);

    /**
     * 增加商品库存
     */
    @Modifying
    @Query("UPDATE Product p SET p.stock = p.stock + :increment WHERE p.id = :productId")
    int increaseStock(@Param("productId") Long productId, @Param("increment") Integer increment);

    /**
     * 更新商品销量
     */
    @Modifying
    @Query("UPDATE Product p SET p.salesCount = p.salesCount + :increment WHERE p.id = :productId")
    int increaseSalesCount(@Param("productId") Long productId, @Param("increment") Integer increment);

    /**
     * 更新商品浏览次数
     */
    @Modifying
    @Query("UPDATE Product p SET p.viewCount = p.viewCount + 1 WHERE p.id = :productId")
    int increaseViewCount(@Param("productId") Long productId);

    /**
     * 更新商品收藏次数
     */
    @Modifying
    @Query("UPDATE Product p SET p.favoriteCount = p.favoriteCount + :increment WHERE p.id = :productId")
    int updateFavoriteCount(@Param("productId") Long productId, @Param("increment") Integer increment);

    /**
     * 更新商品评分和评价数量
     */
    @Modifying
    @Query("UPDATE Product p SET p.rating = :rating, p.reviewCount = :reviewCount WHERE p.id = :productId")
    int updateRatingAndReviewCount(@Param("productId") Long productId, 
                                  @Param("rating") BigDecimal rating, 
                                  @Param("reviewCount") Integer reviewCount);

    /**
     * 批量更新商品状态
     */
    @Modifying
    @Query("UPDATE Product p SET p.status = :status WHERE p.id IN :productIds")
    int updateStatusBatch(@Param("productIds") List<Long> productIds, @Param("status") Integer status);

    /**
     * 更新商品上架时间
     */
    @Modifying
    @Query("UPDATE Product p SET p.publishTime = :publishTime WHERE p.id = :productId")
    int updatePublishTime(@Param("productId") Long productId, @Param("publishTime") LocalDateTime publishTime);

    /**
     * 更新商品下架时间
     */
    @Modifying
    @Query("UPDATE Product p SET p.unpublishTime = :unpublishTime WHERE p.id = :productId")
    int updateUnpublishTime(@Param("productId") Long productId, @Param("unpublishTime") LocalDateTime unpublishTime);

    /**
     * 软删除商品
     */
    @Modifying
    @Query("UPDATE Product p SET p.deleted = true WHERE p.id = :productId")
    int softDeleteProduct(@Param("productId") Long productId);

    /**
     * 批量软删除商品
     */
    @Modifying
    @Query("UPDATE Product p SET p.deleted = true WHERE p.id IN :productIds")
    int softDeleteProductsBatch(@Param("productIds") List<Long> productIds);
}

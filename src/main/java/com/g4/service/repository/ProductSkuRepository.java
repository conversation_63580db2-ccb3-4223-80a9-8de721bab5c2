package com.g4.service.repository;

import com.g4.service.entity.ProductSku;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

/**
 * 商品SKU Repository接口
 * 提供商品SKU数据访问方法
 */
@Repository
public interface ProductSkuRepository extends JpaRepository<ProductSku, Long>, JpaSpecificationExecutor<ProductSku> {

    /**
     * 根据SKU编码查找SKU
     */
    Optional<ProductSku> findBySkuCode(String skuCode);

    /**
     * 检查SKU编码是否存在
     */
    boolean existsBySkuCode(String skuCode);

    /**
     * 根据商品ID查找SKU
     */
    List<ProductSku> findByProductId(Long productId);

    /**
     * 根据商品ID分页查询SKU
     */
    Page<ProductSku> findByProductId(Long productId, Pageable pageable);

    /**
     * 根据SKU状态查找SKU
     */
    List<ProductSku> findByStatus(Integer status);

    /**
     * 根据SKU状态分页查询SKU
     */
    Page<ProductSku> findByStatus(Integer status, Pageable pageable);

    /**
     * 根据商品ID和状态查找SKU
     */
    List<ProductSku> findByProductIdAndStatus(Long productId, Integer status);

    /**
     * 根据商品ID和状态分页查询SKU
     */
    Page<ProductSku> findByProductIdAndStatus(Long productId, Integer status, Pageable pageable);

    /**
     * 根据关键词搜索SKU（SKU编码、SKU名称）
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND " +
           "(s.skuCode LIKE %:keyword% OR s.skuName LIKE %:keyword%)")
    Page<ProductSku> searchSkus(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 根据价格范围查询SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND s.status = 0 AND " +
           "s.price BETWEEN :minPrice AND :maxPrice")
    Page<ProductSku> findByPriceBetween(@Param("minPrice") BigDecimal minPrice, 
                                       @Param("maxPrice") BigDecimal maxPrice, 
                                       Pageable pageable);

    /**
     * 查找所有正常销售的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND s.status = 0 AND s.stock > 0")
    List<ProductSku> findAllOnSale();

    /**
     * 分页查找所有正常销售的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND s.status = 0 AND s.stock > 0")
    Page<ProductSku> findAllOnSale(Pageable pageable);

    /**
     * 查找所有未删除的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false")
    List<ProductSku> findAllActive();

    /**
     * 分页查找所有未删除的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false")
    Page<ProductSku> findAllActive(Pageable pageable);

    /**
     * 查找销量最高的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND s.status = 0 ORDER BY s.salesCount DESC")
    List<ProductSku> findTopSalesSkus(Pageable pageable);

    /**
     * 查找库存不足的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND s.stock <= s.warningStock")
    List<ProductSku> findLowStockSkus();

    /**
     * 查找售罄的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND s.stock = 0")
    List<ProductSku> findSoldOutSkus();

    /**
     * 根据商品ID查找库存不足的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId AND s.stock <= s.warningStock")
    List<ProductSku> findLowStockSkusByProductId(@Param("productId") Long productId);

    /**
     * 根据商品ID查找售罄的SKU
     */
    @Query("SELECT s FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId AND s.stock = 0")
    List<ProductSku> findSoldOutSkusByProductId(@Param("productId") Long productId);

    /**
     * 统计SKU总数
     */
    @Query("SELECT COUNT(s) FROM ProductSku s WHERE s.deleted = false")
    long countActiveSkus();

    /**
     * 根据状态统计SKU数量
     */
    @Query("SELECT COUNT(s) FROM ProductSku s WHERE s.deleted = false AND s.status = :status")
    long countByStatus(@Param("status") Integer status);

    /**
     * 根据商品ID统计SKU数量
     */
    @Query("SELECT COUNT(s) FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId")
    long countByProductId(@Param("productId") Long productId);

    /**
     * 根据商品ID和状态统计SKU数量
     */
    @Query("SELECT COUNT(s) FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId AND s.status = :status")
    long countByProductIdAndStatus(@Param("productId") Long productId, @Param("status") Integer status);

    /**
     * 根据商品ID统计总库存
     */
    @Query("SELECT COALESCE(SUM(s.stock), 0) FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId")
    Integer sumStockByProductId(@Param("productId") Long productId);

    /**
     * 根据商品ID统计可用库存（状态为正常的SKU）
     */
    @Query("SELECT COALESCE(SUM(s.stock), 0) FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId AND s.status = 0")
    Integer sumAvailableStockByProductId(@Param("productId") Long productId);

    /**
     * 根据商品ID获取最低价格
     */
    @Query("SELECT MIN(s.price) FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId AND s.status = 0")
    BigDecimal getMinPriceByProductId(@Param("productId") Long productId);

    /**
     * 根据商品ID获取最高价格
     */
    @Query("SELECT MAX(s.price) FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId AND s.status = 0")
    BigDecimal getMaxPriceByProductId(@Param("productId") Long productId);

    /**
     * 更新SKU库存
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.stock = :stock WHERE s.id = :skuId")
    int updateStock(@Param("skuId") Long skuId, @Param("stock") Integer stock);

    /**
     * 减少SKU库存
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.stock = GREATEST(0, s.stock - :decrement) WHERE s.id = :skuId AND s.stock >= :decrement")
    int decreaseStock(@Param("skuId") Long skuId, @Param("decrement") Integer decrement);

    /**
     * 增加SKU库存
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.stock = s.stock + :increment WHERE s.id = :skuId")
    int increaseStock(@Param("skuId") Long skuId, @Param("increment") Integer increment);

    /**
     * 更新SKU销量
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.salesCount = s.salesCount + :increment WHERE s.id = :skuId")
    int increaseSalesCount(@Param("skuId") Long skuId, @Param("increment") Integer increment);

    /**
     * 更新SKU价格
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.price = :price WHERE s.id = :skuId")
    int updatePrice(@Param("skuId") Long skuId, @Param("price") BigDecimal price);

    /**
     * 批量更新SKU状态
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.status = :status WHERE s.id IN :skuIds")
    int updateStatusBatch(@Param("skuIds") List<Long> skuIds, @Param("status") Integer status);

    /**
     * 根据商品ID批量更新SKU状态
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.status = :status WHERE s.productId = :productId")
    int updateStatusByProductId(@Param("productId") Long productId, @Param("status") Integer status);

    /**
     * 更新SKU排序
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.sort = :sort WHERE s.id = :skuId")
    int updateSort(@Param("skuId") Long skuId, @Param("sort") Integer sort);

    /**
     * 根据商品ID获取最大排序号
     */
    @Query("SELECT COALESCE(MAX(s.sort), 0) FROM ProductSku s WHERE s.deleted = false AND s.productId = :productId")
    Integer getMaxSortByProductId(@Param("productId") Long productId);

    /**
     * 软删除SKU
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.deleted = true WHERE s.id = :skuId")
    int softDeleteSku(@Param("skuId") Long skuId);

    /**
     * 批量软删除SKU
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.deleted = true WHERE s.id IN :skuIds")
    int softDeleteSkusBatch(@Param("skuIds") List<Long> skuIds);

    /**
     * 根据商品ID软删除所有SKU
     */
    @Modifying
    @Query("UPDATE ProductSku s SET s.deleted = true WHERE s.productId = :productId")
    int softDeleteSkusByProductId(@Param("productId") Long productId);
}

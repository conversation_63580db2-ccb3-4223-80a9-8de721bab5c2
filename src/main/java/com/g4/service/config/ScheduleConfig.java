package com.g4.service.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * 定时任务配置类
 * 配置定时任务调度器
 */
@Configuration
@EnableScheduling
public class ScheduleConfig {

    @Value("${app.schedule.pool-size:5}")
    private int poolSize;

    @Value("${app.schedule.thread-name-prefix:schedule-}")
    private String threadNamePrefix;

    @Value("${app.schedule.await-termination-seconds:60}")
    private int awaitTerminationSeconds;

    /**
     * 配置定时任务调度器
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        
        // 线程池大小
        scheduler.setPoolSize(poolSize);
        
        // 线程名前缀
        scheduler.setThreadNamePrefix(threadNamePrefix);
        
        // 等待所有任务结束后再关闭线程池
        scheduler.setWaitForTasksToCompleteOnShutdown(true);
        
        // 等待时间
        scheduler.setAwaitTerminationSeconds(awaitTerminationSeconds);
        
        // 设置拒绝策略
        scheduler.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
        
        scheduler.initialize();
        return scheduler;
    }
}

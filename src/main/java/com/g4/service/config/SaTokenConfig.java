package com.g4.service.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.jwt.StpLogicJwtForSimple;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpLogic;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token安全配置类
 * 配置认证拦截器、路由规则等
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    @Value("${sa-token.jwt-secret-key:your-secret-key}")
    private String jwtSecretKey;

    /**
     * 注册Sa-Token拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册Sa-Token拦截器，校验规则为StpUtil.checkLogin()登录校验
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 指定一条match规则
            SaRouter
                    // 拦截所有路径
                    .match("/**")
                    // 排除登录接口
                    .notMatch("/api/auth/login")
                    .notMatch("/api/auth/register")
                    .notMatch("/api/auth/logout")
                    .notMatch("/api/auth/refresh")
                    // 排除公开接口
                    .notMatch("/api/public/**")
                    // 排除静态资源
                    .notMatch("/static/**")
                    .notMatch("/favicon.ico")
                    .notMatch("/error")
                    // 排除Swagger文档
                    .notMatch("/swagger-ui/**")
                    .notMatch("/v3/api-docs/**")
                    .notMatch("/swagger-resources/**")
                    .notMatch("/webjars/**")
                    // 排除健康检查
                    .notMatch("/actuator/**")
                    // 排除开发工具
                    .notMatch("/h2-console/**")
                    // 执行认证函数
                    .check(r -> StpUtil.checkLogin());

            // 角色权限校验
            SaRouter
                    // 管理员接口
                    .match("/api/admin/**")
                    .check(r -> StpUtil.checkRole("admin"));

            // 权限校验
            SaRouter
                    // 用户管理权限
                    .match("/api/user/manage/**")
                    .check(r -> StpUtil.checkPermission("user:manage"));

            SaRouter
                    // 店铺管理权限
                    .match("/api/shop/manage/**")
                    .check(r -> StpUtil.checkPermission("shop:manage"));

            SaRouter
                    // 商品管理权限
                    .match("/api/product/manage/**")
                    .check(r -> StpUtil.checkPermission("product:manage"));

            // 店主权限校验
            SaRouter
                    // 店主接口
                    .match("/api/shop/owner/**")
                    .check(r -> {
                        // 检查是否登录
                        StpUtil.checkLogin();
                        // 检查是否有店主角色或管理员角色
                        if (!StpUtil.hasRole("shop_owner") && !StpUtil.hasRole("admin")) {
                            throw new RuntimeException("无权限访问");
                        }
                    });

        })).addPathPatterns("/**");
    }

    /**
     * 配置Sa-Token的JWT模式
     */
    @Bean
    public StpLogic getStpLogicJwt() {
        return new StpLogicJwtForSimple();
    }

    /**
     * Sa-Token配置类
     */
    @Configuration
    public static class SaTokenConfiguration {

        /**
         * 获取配置信息
         */
        @Bean
        public cn.dev33.satoken.config.SaTokenConfig getSaTokenConfig(
                @Value("${sa-token.token-name:Authorization}") String tokenName,
                @Value("${sa-token.timeout:7200}") long timeout,
                @Value("${sa-token.activity-timeout:1800}") long activityTimeout,
                @Value("${sa-token.is-concurrent:true}") boolean isConcurrent,
                @Value("${sa-token.is-share:false}") boolean isShare,
                @Value("${sa-token.max-login-count:5}") int maxLoginCount,
                @Value("${sa-token.is-log:false}") boolean isLog) {

            cn.dev33.satoken.config.SaTokenConfig config = new cn.dev33.satoken.config.SaTokenConfig();
            
            // token名称 (同时也是cookie名称)
            config.setTokenName(tokenName);
            
            // token有效期，单位s 默认30天, -1代表永不过期
            config.setTimeout(timeout);
            
            // token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒
            config.setActivityTimeout(activityTimeout);
            
            // 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
            config.setIsConcurrent(isConcurrent);
            
            // 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
            config.setIsShare(isShare);
            
            // 同一账号最大登录数量，-1代表不限 （只有在 isConcurrent=true, isShare=false 时此配置才有效）
            config.setMaxLoginCount(maxLoginCount);
            
            // 是否尝试从请求体里读取token
            config.setIsReadBody(false);
            
            // 是否尝试从header里读取token
            config.setIsReadHeader(true);
            
            // 是否尝试从cookie里读取token
            config.setIsReadCookie(false);
            
            // 是否输出操作日志
            config.setIsLog(isLog);
            
            return config;
        }
    }

    /**
     * 自定义权限验证接口扩展
     */
    /*
    @Component
    public class StpInterfaceImpl implements StpInterface {

        @Autowired
        private UserService userService;

        @Override
        public List<String> getPermissionList(Object loginId, String loginType) {
            // 返回此loginId拥有的权限列表
            return userService.getPermissionList(Long.valueOf(loginId.toString()));
        }

        @Override
        public List<String> getRoleList(Object loginId, String loginType) {
            // 返回此loginId拥有的角色列表
            return userService.getRoleList(Long.valueOf(loginId.toString()));
        }
    }
    */
}

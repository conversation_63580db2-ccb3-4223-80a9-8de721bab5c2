package com.g4.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 开发环境配置类
 * 仅在开发环境下生效的配置
 */
@Slf4j
@Configuration
@Profile({"dev", "development"})
public class DevConfig implements WebMvcConfigurer {

    /**
     * 开发环境静态资源配置
     */
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        log.info("配置开发环境静态资源映射");
        
        // 开发环境下允许访问更多静态资源
        registry.addResourceHandler("/dev/**")
                .addResourceLocations("classpath:/dev/");
        
        // 开发工具资源
        registry.addResourceHandler("/dev-tools/**")
                .addResourceLocations("classpath:/static/dev-tools/");
    }

    /**
     * 开发环境数据初始化器
     */
    @Bean
    @ConditionalOnProperty(name = "app.dev.init-data", havingValue = "true", matchIfMissing = true)
    public DevDataInitializer devDataInitializer() {
        return new DevDataInitializer();
    }

    /**
     * 开发环境数据初始化器实现
     */
    public static class DevDataInitializer {
        
        public DevDataInitializer() {
            log.info("开发环境数据初始化器已创建");
            initializeDevData();
        }

        /**
         * 初始化开发数据
         */
        private void initializeDevData() {
            log.info("开始初始化开发环境数据...");
            
            try {
                // 初始化测试用户
                initTestUsers();
                
                // 初始化测试店铺
                initTestShops();
                
                // 初始化测试商品
                initTestProducts();
                
                // 初始化测试订单
                initTestOrders();
                
                log.info("开发环境数据初始化完成");
            } catch (Exception e) {
                log.error("开发环境数据初始化失败", e);
            }
        }

        /**
         * 初始化测试用户
         */
        private void initTestUsers() {
            log.info("初始化测试用户数据...");
            // 这里可以添加测试用户创建逻辑
            // 例如：创建管理员用户、普通用户、店主用户等
        }

        /**
         * 初始化测试店铺
         */
        private void initTestShops() {
            log.info("初始化测试店铺数据...");
            // 这里可以添加测试店铺创建逻辑
        }

        /**
         * 初始化测试商品
         */
        private void initTestProducts() {
            log.info("初始化测试商品数据...");
            // 这里可以添加测试商品创建逻辑
        }

        /**
         * 初始化测试订单
         */
        private void initTestOrders() {
            log.info("初始化测试订单数据...");
            // 这里可以添加测试订单创建逻辑
        }
    }

    /**
     * 开发环境Mock服务配置
     */
    @Bean
    @ConditionalOnProperty(name = "app.dev.mock-enabled", havingValue = "true")
    public MockServiceConfig mockServiceConfig() {
        return new MockServiceConfig();
    }

    /**
     * Mock服务配置类
     */
    public static class MockServiceConfig {
        
        public MockServiceConfig() {
            log.info("开发环境Mock服务已启用");
        }

        /**
         * Mock支付服务
         */
        public void mockPaymentService() {
            log.info("Mock支付服务已配置");
            // 这里可以配置Mock支付服务
        }

        /**
         * Mock短信服务
         */
        public void mockSmsService() {
            log.info("Mock短信服务已配置");
            // 这里可以配置Mock短信服务
        }

        /**
         * Mock邮件服务
         */
        public void mockEmailService() {
            log.info("Mock邮件服务已配置");
            // 这里可以配置Mock邮件服务
        }
    }
}

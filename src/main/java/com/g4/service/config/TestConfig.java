package com.g4.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * 测试环境配置类
 * 仅在测试环境下生效的配置
 */
@Slf4j
@Configuration
@Profile({"test", "testing"})
public class TestConfig {

    /**
     * 测试环境数据清理器
     */
    @Bean
    @ConditionalOnProperty(name = "app.test.cleanup-enabled", havingValue = "true", matchIfMissing = true)
    public TestDataCleaner testDataCleaner() {
        return new TestDataCleaner();
    }

    /**
     * 测试数据清理器实现
     */
    public static class TestDataCleaner {
        
        public TestDataCleaner() {
            log.info("测试环境数据清理器已创建");
        }

        /**
         * 清理测试数据
         */
        public void cleanupTestData() {
            log.info("开始清理测试数据...");
            
            try {
                // 清理测试订单
                cleanupTestOrders();
                
                // 清理测试商品
                cleanupTestProducts();
                
                // 清理测试店铺
                cleanupTestShops();
                
                // 清理测试用户
                cleanupTestUsers();
                
                log.info("测试数据清理完成");
            } catch (Exception e) {
                log.error("测试数据清理失败", e);
            }
        }

        private void cleanupTestOrders() {
            log.info("清理测试订单数据...");
            // 这里可以添加测试订单清理逻辑
        }

        private void cleanupTestProducts() {
            log.info("清理测试商品数据...");
            // 这里可以添加测试商品清理逻辑
        }

        private void cleanupTestShops() {
            log.info("清理测试店铺数据...");
            // 这里可以添加测试店铺清理逻辑
        }

        private void cleanupTestUsers() {
            log.info("清理测试用户数据...");
            // 这里可以添加测试用户清理逻辑
        }
    }

    /**
     * 测试环境性能监控
     */
    @Bean
    @ConditionalOnProperty(name = "app.test.performance-monitor", havingValue = "true")
    public TestPerformanceMonitor testPerformanceMonitor() {
        return new TestPerformanceMonitor();
    }

    /**
     * 测试性能监控器实现
     */
    public static class TestPerformanceMonitor {
        
        public TestPerformanceMonitor() {
            log.info("测试环境性能监控器已启用");
        }

        /**
         * 监控API性能
         */
        public void monitorApiPerformance() {
            log.info("API性能监控已启用");
            // 这里可以添加API性能监控逻辑
        }

        /**
         * 监控数据库性能
         */
        public void monitorDatabasePerformance() {
            log.info("数据库性能监控已启用");
            // 这里可以添加数据库性能监控逻辑
        }

        /**
         * 监控缓存性能
         */
        public void monitorCachePerformance() {
            log.info("缓存性能监控已启用");
            // 这里可以添加缓存性能监控逻辑
        }
    }
}

package com.g4.service.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * JPA配置类
 * 启用JPA审计功能和Repository扫描
 */
@Configuration
@EnableJpaAuditing
@EnableJpaRepositories(basePackages = "com.g4.service.repository")
public class JpaConfig {
    
    // JPA相关配置可以在这里添加
    // 例如：自定义审计字段填充逻辑、数据源配置等
}

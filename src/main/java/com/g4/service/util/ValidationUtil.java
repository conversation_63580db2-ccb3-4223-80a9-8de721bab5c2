package com.g4.service.util;

import com.g4.service.common.exception.BusinessException;
import com.g4.service.common.enums.ResponseCode;

import java.util.Collection;
import java.util.regex.Pattern;

/**
 * 数据校验工具类
 */
public class ValidationUtil {
    
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^1[3-9]\\d{9}$"
    );
    
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(
        "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"
    );
    
    /**
     * 断言对象不为null
     */
    public static void notNull(Object object, String message) {
        if (object == null) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言字符串不为空
     */
    public static void notEmpty(String str, String message) {
        if (StringUtil.isEmpty(str)) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言字符串不为空白
     */
    public static void notBlank(String str, String message) {
        if (StringUtil.isBlank(str)) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言集合不为空
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (collection == null || collection.isEmpty()) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言数组不为空
     */
    public static void notEmpty(Object[] array, String message) {
        if (array == null || array.length == 0) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言条件为真
     */
    public static void isTrue(boolean condition, String message) {
        if (!condition) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言条件为假
     */
    public static void isFalse(boolean condition, String message) {
        if (condition) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言数字为正数
     */
    public static void isPositive(Number number, String message) {
        notNull(number, message);
        if (number.doubleValue() <= 0) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言数字不为负数
     */
    public static void isNotNegative(Number number, String message) {
        notNull(number, message);
        if (number.doubleValue() < 0) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言字符串长度在指定范围内
     */
    public static void lengthBetween(String str, int min, int max, String message) {
        notNull(str, message);
        int length = str.length();
        if (length < min || length > max) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 断言数字在指定范围内
     */
    public static void between(Number number, Number min, Number max, String message) {
        notNull(number, message);
        double value = number.doubleValue();
        double minValue = min.doubleValue();
        double maxValue = max.doubleValue();
        if (value < minValue || value > maxValue) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 验证邮箱格式
     */
    public static void validEmail(String email, String message) {
        notBlank(email, message);
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 验证手机号格式
     */
    public static void validPhone(String phone, String message) {
        notBlank(phone, message);
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 验证身份证号格式
     */
    public static void validIdCard(String idCard, String message) {
        notBlank(idCard, message);
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 验证密码强度（至少8位，包含字母和数字）
     */
    public static void validPassword(String password, String message) {
        notBlank(password, message);
        if (password.length() < 8) {
            throw BusinessException.paramError(message);
        }
        boolean hasLetter = password.matches(".*[a-zA-Z].*");
        boolean hasDigit = password.matches(".*\\d.*");
        if (!hasLetter || !hasDigit) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 验证两个字符串相等
     */
    public static void equals(String str1, String str2, String message) {
        if (!StringUtil.equals(str1, str2)) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 验证字符串匹配正则表达式
     */
    public static void matches(String str, String regex, String message) {
        notBlank(str, message);
        if (!str.matches(regex)) {
            throw BusinessException.paramError(message);
        }
    }
    
    /**
     * 业务断言 - 抛出业务异常
     */
    public static void businessAssert(boolean condition, ResponseCode responseCode) {
        if (!condition) {
            throw new BusinessException(responseCode);
        }
    }
    
    /**
     * 业务断言 - 抛出业务异常（自定义消息）
     */
    public static void businessAssert(boolean condition, ResponseCode responseCode, String message) {
        if (!condition) {
            throw new BusinessException(responseCode, message);
        }
    }
}

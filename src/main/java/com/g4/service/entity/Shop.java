package com.g4.service.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 店铺实体类
 * 管理店铺基本信息、状态和业务数据
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "biz_shop", indexes = {
    @Index(name = "idx_shop_code", columnList = "shop_code", unique = true),
    @Index(name = "idx_shop_name", columnList = "shop_name"),
    @Index(name = "idx_owner_id", columnList = "owner_id"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_create_time", columnList = "create_time")
})
public class Shop extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺编码（唯一标识）
     */
    @NotBlank(message = "店铺编码不能为空")
    @Size(max = 50, message = "店铺编码长度不能超过50个字符")
    @Column(name = "shop_code", nullable = false, unique = true, length = 50)
    private String shopCode;

    /**
     * 店铺名称
     */
    @NotBlank(message = "店铺名称不能为空")
    @Size(max = 100, message = "店铺名称长度不能超过100个字符")
    @Column(name = "shop_name", nullable = false, length = 100)
    private String shopName;

    /**
     * 店铺简介
     */
    @Size(max = 500, message = "店铺简介长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 店铺LOGO
     */
    @Size(max = 500, message = "店铺LOGO长度不能超过500个字符")
    @Column(name = "logo", length = 500)
    private String logo;

    /**
     * 店铺封面图
     */
    @Size(max = 500, message = "店铺封面图长度不能超过500个字符")
    @Column(name = "cover_image", length = 500)
    private String coverImage;

    /**
     * 店主用户ID
     */
    @Column(name = "owner_id", nullable = false)
    private Long ownerId;

    /**
     * 店铺类型（0：个人店铺，1：企业店铺）
     */
    @Column(name = "shop_type", nullable = false)
    private Integer shopType = 0;

    /**
     * 店铺状态（0：正常营业，1：暂停营业，2：关闭，3：审核中）
     */
    @Column(name = "status", nullable = false)
    private Integer status = 3;

    /**
     * 联系电话
     */
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @Column(name = "contact_phone", length = 20)
    private String contactPhone;

    /**
     * 联系邮箱
     */
    @Size(max = 100, message = "联系邮箱长度不能超过100个字符")
    @Column(name = "contact_email", length = 100)
    private String contactEmail;

    /**
     * 店铺地址
     */
    @Size(max = 200, message = "店铺地址长度不能超过200个字符")
    @Column(name = "address", length = 200)
    private String address;

    /**
     * 经度
     */
    @DecimalMin(value = "-180.0", message = "经度值必须在-180到180之间")
    @DecimalMax(value = "180.0", message = "经度值必须在-180到180之间")
    @Column(name = "longitude", precision = 10, scale = 7)
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @DecimalMin(value = "-90.0", message = "纬度值必须在-90到90之间")
    @DecimalMax(value = "90.0", message = "纬度值必须在-90到90之间")
    @Column(name = "latitude", precision = 10, scale = 7)
    private BigDecimal latitude;

    /**
     * 营业开始时间
     */
    @Size(max = 10, message = "营业开始时间格式不正确")
    @Column(name = "business_start_time", length = 10)
    private String businessStartTime;

    /**
     * 营业结束时间
     */
    @Size(max = 10, message = "营业结束时间格式不正确")
    @Column(name = "business_end_time", length = 10)
    private String businessEndTime;

    /**
     * 店铺评分
     */
    @DecimalMin(value = "0.0", message = "店铺评分不能小于0")
    @DecimalMax(value = "5.0", message = "店铺评分不能大于5")
    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;

    /**
     * 评价数量
     */
    @Column(name = "review_count")
    private Integer reviewCount = 0;

    /**
     * 商品数量
     */
    @Column(name = "product_count")
    private Integer productCount = 0;

    /**
     * 销售总额
     */
    @Column(name = "total_sales", precision = 15, scale = 2)
    private BigDecimal totalSales = BigDecimal.ZERO;

    /**
     * 开店时间
     */
    @Column(name = "open_time")
    private LocalDateTime openTime;

    /**
     * 关店时间
     */
    @Column(name = "close_time")
    private LocalDateTime closeTime;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 店主用户关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "owner_id", insertable = false, updatable = false)
    private User owner;

    /**
     * 店铺商品列表
     */
    @OneToMany(mappedBy = "shop", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Product> products;

    // ========== 便捷方法 ==========

    /**
     * 是否正常营业
     */
    public boolean isOpen() {
        return status != null && status == 0;
    }

    /**
     * 是否暂停营业
     */
    public boolean isPaused() {
        return status != null && status == 1;
    }

    /**
     * 是否已关闭
     */
    public boolean isClosed() {
        return status != null && status == 2;
    }

    /**
     * 是否审核中
     */
    public boolean isPending() {
        return status != null && status == 3;
    }

    /**
     * 是否企业店铺
     */
    public boolean isEnterprise() {
        return shopType != null && shopType == 1;
    }

    /**
     * 增加商品数量
     */
    public void increaseProductCount() {
        this.productCount = (this.productCount == null ? 0 : this.productCount) + 1;
    }

    /**
     * 减少商品数量
     */
    public void decreaseProductCount() {
        this.productCount = Math.max(0, (this.productCount == null ? 0 : this.productCount) - 1);
    }

    /**
     * 增加销售额
     */
    public void addSales(BigDecimal amount) {
        if (amount != null && amount.compareTo(BigDecimal.ZERO) > 0) {
            this.totalSales = (this.totalSales == null ? BigDecimal.ZERO : this.totalSales).add(amount);
        }
    }
}

package com.g4.service.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 角色实体类
 * 管理系统角色信息和权限分配
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sys_role", indexes = {
    @Index(name = "idx_role_code", columnList = "role_code", unique = true),
    @Index(name = "idx_role_name", columnList = "role_name"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_sort", columnList = "sort")
})
public class Role extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 角色编码（唯一标识）
     */
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    @Column(name = "role_code", nullable = false, unique = true, length = 50)
    private String roleCode;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    @Column(name = "role_name", nullable = false, length = 100)
    private String roleName;

    /**
     * 角色描述
     */
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 角色状态（0：正常，1：禁用）
     */
    @Column(name = "status", nullable = false)
    private Integer status = 0;

    /**
     * 排序号
     */
    @Column(name = "sort")
    private Integer sort = 0;

    /**
     * 数据权限范围（0：全部数据，1：本部门数据，2：本部门及子部门数据，3：仅本人数据）
     */
    @Column(name = "data_scope")
    private Integer dataScope = 0;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 用户角色关联
     */
    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<UserRole> userRoles;

    /**
     * 角色权限关联
     */
    @OneToMany(mappedBy = "role", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<RolePermission> rolePermissions;

    // ========== 便捷方法 ==========

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 0;
    }

    /**
     * 是否为系统内置角色
     */
    public boolean isSystemRole() {
        return roleCode != null && (
            roleCode.equals("SUPER_ADMIN") || 
            roleCode.equals("ADMIN") || 
            roleCode.equals("USER")
        );
    }
}

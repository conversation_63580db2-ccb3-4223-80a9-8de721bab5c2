package com.g4.service.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品实体类
 * 管理商品基本信息、价格、库存和状态
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "biz_product", indexes = {
    @Index(name = "idx_product_code", columnList = "product_code", unique = true),
    @Index(name = "idx_product_name", columnList = "product_name"),
    @Index(name = "idx_shop_id", columnList = "shop_id"),
    @Index(name = "idx_category_id", columnList = "category_id"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_price", columnList = "price"),
    @Index(name = "idx_create_time", columnList = "create_time")
})
public class Product extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 商品编码（唯一标识）
     */
    @NotBlank(message = "商品编码不能为空")
    @Size(max = 50, message = "商品编码长度不能超过50个字符")
    @Column(name = "product_code", nullable = false, unique = true, length = 50)
    private String productCode;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    @Size(max = 200, message = "商品名称长度不能超过200个字符")
    @Column(name = "product_name", nullable = false, length = 200)
    private String productName;

    /**
     * 商品简介
     */
    @Size(max = 500, message = "商品简介长度不能超过500个字符")
    @Column(name = "summary", length = 500)
    private String summary;

    /**
     * 商品详情
     */
    @Lob
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;

    /**
     * 所属店铺ID
     */
    @NotNull(message = "所属店铺不能为空")
    @Column(name = "shop_id", nullable = false)
    private Long shopId;

    /**
     * 商品分类ID
     */
    @NotNull(message = "商品分类不能为空")
    @Column(name = "category_id", nullable = false)
    private Long categoryId;

    /**
     * 品牌名称
     */
    @Size(max = 100, message = "品牌名称长度不能超过100个字符")
    @Column(name = "brand", length = 100)
    private String brand;

    /**
     * 商品主图
     */
    @Size(max = 500, message = "商品主图长度不能超过500个字符")
    @Column(name = "main_image", length = 500)
    private String mainImage;

    /**
     * 商品图片列表（JSON格式存储）
     */
    @Lob
    @Column(name = "images", columnDefinition = "TEXT")
    private String images;

    /**
     * 商品价格
     */
    @NotNull(message = "商品价格不能为空")
    @DecimalMin(value = "0.01", message = "商品价格必须大于0")
    @Column(name = "price", nullable = false, precision = 10, scale = 2)
    private BigDecimal price;

    /**
     * 原价（用于显示折扣）
     */
    @DecimalMin(value = "0.00", message = "原价不能小于0")
    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    /**
     * 成本价
     */
    @DecimalMin(value = "0.00", message = "成本价不能小于0")
    @Column(name = "cost_price", precision = 10, scale = 2)
    private BigDecimal costPrice;

    /**
     * 库存数量
     */
    @NotNull(message = "库存数量不能为空")
    @Min(value = 0, message = "库存数量不能小于0")
    @Column(name = "stock", nullable = false)
    private Integer stock;

    /**
     * 预警库存
     */
    @Min(value = 0, message = "预警库存不能小于0")
    @Column(name = "warning_stock")
    private Integer warningStock = 10;

    /**
     * 商品重量（克）
     */
    @DecimalMin(value = "0.00", message = "商品重量不能小于0")
    @Column(name = "weight", precision = 8, scale = 2)
    private BigDecimal weight;

    /**
     * 商品体积（立方厘米）
     */
    @DecimalMin(value = "0.00", message = "商品体积不能小于0")
    @Column(name = "volume", precision = 10, scale = 2)
    private BigDecimal volume;

    /**
     * 商品状态（0：正常，1：下架，2：售罄，3：审核中）
     */
    @Column(name = "status", nullable = false)
    private Integer status = 3;

    /**
     * 是否推荐（0：否，1：是）
     */
    @Column(name = "is_recommended")
    private Boolean isRecommended = false;

    /**
     * 是否新品（0：否，1：是）
     */
    @Column(name = "is_new")
    private Boolean isNew = true;

    /**
     * 是否热销（0：否，1：是）
     */
    @Column(name = "is_hot")
    private Boolean isHot = false;

    /**
     * 销售数量
     */
    @Column(name = "sales_count")
    private Integer salesCount = 0;

    /**
     * 浏览次数
     */
    @Column(name = "view_count")
    private Integer viewCount = 0;

    /**
     * 收藏次数
     */
    @Column(name = "favorite_count")
    private Integer favoriteCount = 0;

    /**
     * 商品评分
     */
    @DecimalMin(value = "0.0", message = "商品评分不能小于0")
    @DecimalMax(value = "5.0", message = "商品评分不能大于5")
    @Column(name = "rating", precision = 3, scale = 2)
    private BigDecimal rating = BigDecimal.ZERO;

    /**
     * 评价数量
     */
    @Column(name = "review_count")
    private Integer reviewCount = 0;

    /**
     * 排序号
     */
    @Column(name = "sort")
    private Integer sort = 0;

    /**
     * 上架时间
     */
    @Column(name = "publish_time")
    private LocalDateTime publishTime;

    /**
     * 下架时间
     */
    @Column(name = "unpublish_time")
    private LocalDateTime unpublishTime;

    /**
     * SEO关键词
     */
    @Size(max = 200, message = "SEO关键词长度不能超过200个字符")
    @Column(name = "keywords", length = 200)
    private String keywords;

    /**
     * SEO描述
     */
    @Size(max = 500, message = "SEO描述长度不能超过500个字符")
    @Column(name = "seo_description", length = 500)
    private String seoDescription;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 所属店铺关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "shop_id", insertable = false, updatable = false)
    private Shop shop;

    /**
     * 商品分类关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "category_id", insertable = false, updatable = false)
    private Category category;

    /**
     * 商品规格列表
     */
    @OneToMany(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<ProductSku> skus;

    // ========== 便捷方法 ==========

    /**
     * 是否正常销售
     */
    public boolean isOnSale() {
        return status != null && status == 0 && stock != null && stock > 0;
    }

    /**
     * 是否下架
     */
    public boolean isOffShelf() {
        return status != null && status == 1;
    }

    /**
     * 是否售罄
     */
    public boolean isSoldOut() {
        return status != null && status == 2 || (stock != null && stock <= 0);
    }

    /**
     * 是否审核中
     */
    public boolean isPending() {
        return status != null && status == 3;
    }

    /**
     * 是否库存不足
     */
    public boolean isLowStock() {
        return stock != null && warningStock != null && stock <= warningStock;
    }

    /**
     * 增加销售数量
     */
    public void increaseSalesCount(Integer count) {
        if (count != null && count > 0) {
            this.salesCount = (this.salesCount == null ? 0 : this.salesCount) + count;
        }
    }

    /**
     * 减少库存
     */
    public boolean decreaseStock(Integer count) {
        if (count != null && count > 0 && stock != null && stock >= count) {
            this.stock -= count;
            return true;
        }
        return false;
    }

    /**
     * 增加库存
     */
    public void increaseStock(Integer count) {
        if (count != null && count > 0) {
            this.stock = (this.stock == null ? 0 : this.stock) + count;
        }
    }

    /**
     * 增加浏览次数
     */
    public void increaseViewCount() {
        this.viewCount = (this.viewCount == null ? 0 : this.viewCount) + 1;
    }

    /**
     * 增加收藏次数
     */
    public void increaseFavoriteCount() {
        this.favoriteCount = (this.favoriteCount == null ? 0 : this.favoriteCount) + 1;
    }

    /**
     * 减少收藏次数
     */
    public void decreaseFavoriteCount() {
        this.favoriteCount = Math.max(0, (this.favoriteCount == null ? 0 : this.favoriteCount) - 1);
    }
}

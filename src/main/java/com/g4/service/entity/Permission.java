package com.g4.service.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 权限实体类
 * 管理系统权限信息，支持菜单权限和按钮权限
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sys_permission", indexes = {
    @Index(name = "idx_permission_code", columnList = "permission_code", unique = true),
    @Index(name = "idx_parent_id", columnList = "parent_id"),
    @Index(name = "idx_type", columnList = "type"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_sort", columnList = "sort")
})
public class Permission extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 权限编码（唯一标识）
     */
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    @Column(name = "permission_code", nullable = false, unique = true, length = 100)
    private String permissionCode;

    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    @Column(name = "permission_name", nullable = false, length = 100)
    private String permissionName;

    /**
     * 父权限ID（用于构建权限树）
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 权限类型（0：目录，1：菜单，2：按钮）
     */
    @Column(name = "type", nullable = false)
    private Integer type;

    /**
     * 权限路径/URL
     */
    @Size(max = 200, message = "权限路径长度不能超过200个字符")
    @Column(name = "path", length = 200)
    private String path;

    /**
     * 组件路径
     */
    @Size(max = 200, message = "组件路径长度不能超过200个字符")
    @Column(name = "component", length = 200)
    private String component;

    /**
     * 权限图标
     */
    @Size(max = 100, message = "权限图标长度不能超过100个字符")
    @Column(name = "icon", length = 100)
    private String icon;

    /**
     * 排序号
     */
    @Column(name = "sort")
    private Integer sort = 0;

    /**
     * 权限状态（0：正常，1：禁用）
     */
    @Column(name = "status", nullable = false)
    private Integer status = 0;

    /**
     * 是否外链（0：否，1：是）
     */
    @Column(name = "is_external")
    private Boolean isExternal = false;

    /**
     * 是否缓存（0：否，1：是）
     */
    @Column(name = "is_cache")
    private Boolean isCache = false;

    /**
     * 是否显示（0：否，1：是）
     */
    @Column(name = "is_visible")
    private Boolean isVisible = true;

    /**
     * 权限描述
     */
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 角色权限关联
     */
    @OneToMany(mappedBy = "permission", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<RolePermission> rolePermissions;

    /**
     * 子权限列表
     */
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    @JsonIgnore
    private List<Permission> children;

    // ========== 便捷方法 ==========

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 0;
    }

    /**
     * 是否为目录
     */
    public boolean isDirectory() {
        return type != null && type == 0;
    }

    /**
     * 是否为菜单
     */
    public boolean isMenu() {
        return type != null && type == 1;
    }

    /**
     * 是否为按钮
     */
    public boolean isButton() {
        return type != null && type == 2;
    }

    /**
     * 是否为根权限
     */
    public boolean isRoot() {
        return parentId == null || parentId == 0;
    }
}

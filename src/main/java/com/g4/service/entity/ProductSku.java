package com.g4.service.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 商品SKU实体类
 * 管理商品的具体规格信息，如颜色、尺寸等
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "biz_product_sku", indexes = {
    @Index(name = "idx_sku_code", columnList = "sku_code", unique = true),
    @Index(name = "idx_product_id", columnList = "product_id"),
    @Index(name = "idx_status", columnList = "status")
})
public class ProductSku extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * SKU编码（唯一标识）
     */
    @NotBlank(message = "SKU编码不能为空")
    @Size(max = 50, message = "SKU编码长度不能超过50个字符")
    @Column(name = "sku_code", nullable = false, unique = true, length = 50)
    private String skuCode;

    /**
     * 商品ID
     */
    @Column(name = "product_id", nullable = false)
    private Long productId;

    /**
     * SKU名称
     */
    @Size(max = 200, message = "SKU名称长度不能超过200个字符")
    @Column(name = "sku_name", length = 200)
    private String skuName;

    /**
     * 规格属性（JSON格式存储，如：{"颜色":"红色","尺寸":"L"}）
     */
    @Lob
    @Column(name = "attributes", columnDefinition = "TEXT")
    private String attributes;

    /**
     * SKU图片
     */
    @Size(max = 500, message = "SKU图片长度不能超过500个字符")
    @Column(name = "image", length = 500)
    private String image;

    /**
     * SKU价格
     */
    @DecimalMin(value = "0.01", message = "SKU价格必须大于0")
    @Column(name = "price", precision = 10, scale = 2)
    private BigDecimal price;

    /**
     * SKU原价
     */
    @DecimalMin(value = "0.00", message = "SKU原价不能小于0")
    @Column(name = "original_price", precision = 10, scale = 2)
    private BigDecimal originalPrice;

    /**
     * SKU成本价
     */
    @DecimalMin(value = "0.00", message = "SKU成本价不能小于0")
    @Column(name = "cost_price", precision = 10, scale = 2)
    private BigDecimal costPrice;

    /**
     * SKU库存
     */
    @Min(value = 0, message = "SKU库存不能小于0")
    @Column(name = "stock")
    private Integer stock = 0;

    /**
     * SKU预警库存
     */
    @Min(value = 0, message = "SKU预警库存不能小于0")
    @Column(name = "warning_stock")
    private Integer warningStock = 5;

    /**
     * SKU重量（克）
     */
    @DecimalMin(value = "0.00", message = "SKU重量不能小于0")
    @Column(name = "weight", precision = 8, scale = 2)
    private BigDecimal weight;

    /**
     * SKU体积（立方厘米）
     */
    @DecimalMin(value = "0.00", message = "SKU体积不能小于0")
    @Column(name = "volume", precision = 10, scale = 2)
    private BigDecimal volume;

    /**
     * SKU状态（0：正常，1：禁用）
     */
    @Column(name = "status", nullable = false)
    private Integer status = 0;

    /**
     * 销售数量
     */
    @Column(name = "sales_count")
    private Integer salesCount = 0;

    /**
     * 排序号
     */
    @Column(name = "sort")
    private Integer sort = 0;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 商品关联
     */
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;

    // ========== 便捷方法 ==========

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 0;
    }

    /**
     * 是否有库存
     */
    public boolean hasStock() {
        return stock != null && stock > 0;
    }

    /**
     * 是否库存不足
     */
    public boolean isLowStock() {
        return stock != null && warningStock != null && stock <= warningStock;
    }

    /**
     * 减少库存
     */
    public boolean decreaseStock(Integer count) {
        if (count != null && count > 0 && stock != null && stock >= count) {
            this.stock -= count;
            return true;
        }
        return false;
    }

    /**
     * 增加库存
     */
    public void increaseStock(Integer count) {
        if (count != null && count > 0) {
            this.stock = (this.stock == null ? 0 : this.stock) + count;
        }
    }

    /**
     * 增加销售数量
     */
    public void increaseSalesCount(Integer count) {
        if (count != null && count > 0) {
            this.salesCount = (this.salesCount == null ? 0 : this.salesCount) + count;
        }
    }
}

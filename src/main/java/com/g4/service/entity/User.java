package com.g4.service.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户实体类
 * 管理系统用户信息，包括基本信息、认证信息和状态管理
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "sys_user", indexes = {
    @Index(name = "idx_username", columnList = "username", unique = true),
    @Index(name = "idx_email", columnList = "email", unique = true),
    @Index(name = "idx_phone", columnList = "phone"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_create_time", columnList = "create_time")
})
public class User extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户名（登录账号）
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 20, message = "用户名长度必须在3-20个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    @Column(name = "username", nullable = false, unique = true, length = 20)
    private String username;

    /**
     * 密码（加密存储）
     */
    @JsonIgnore
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 100, message = "密码长度必须在6-100个字符之间")
    @Column(name = "password", nullable = false, length = 100)
    private String password;

    /**
     * 用户昵称
     */
    @Size(max = 50, message = "昵称长度不能超过50个字符")
    @Column(name = "nickname", length = 50)
    private String nickname;

    /**
     * 真实姓名
     */
    @Size(max = 20, message = "真实姓名长度不能超过20个字符")
    @Column(name = "real_name", length = 20)
    private String realName;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    @Column(name = "email", unique = true, length = 100)
    private String email;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @Column(name = "phone", length = 11)
    private String phone;

    /**
     * 头像URL
     */
    @Size(max = 500, message = "头像URL长度不能超过500个字符")
    @Column(name = "avatar", length = 500)
    private String avatar;

    /**
     * 性别（0：未知，1：男，2：女）
     */
    @Column(name = "gender")
    private Integer gender = 0;

    /**
     * 生日
     */
    @Column(name = "birthday")
    private LocalDateTime birthday;

    /**
     * 用户状态（0：正常，1：禁用，2：锁定）
     */
    @Column(name = "status", nullable = false)
    private Integer status = 0;

    /**
     * 用户类型（0：普通用户，1：管理员，2：超级管理员）
     */
    @Column(name = "user_type", nullable = false)
    private Integer userType = 0;

    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @Size(max = 50, message = "IP地址长度不能超过50个字符")
    @Column(name = "last_login_ip", length = 50)
    private String lastLoginIp;

    /**
     * 登录次数
     */
    @Column(name = "login_count")
    private Integer loginCount = 0;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 用户拥有的店铺列表
     */
    @OneToMany(mappedBy = "owner", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Shop> shops;

    /**
     * 用户角色关联
     */
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<UserRole> userRoles;

    // ========== 便捷方法 ==========

    /**
     * 是否为管理员
     */
    public boolean isAdmin() {
        return userType != null && userType >= 1;
    }

    /**
     * 是否为超级管理员
     */
    public boolean isSuperAdmin() {
        return userType != null && userType == 2;
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 0;
    }

    /**
     * 是否被锁定
     */
    public boolean isLocked() {
        return status != null && status == 2;
    }

    /**
     * 更新登录信息
     */
    public void updateLoginInfo(String loginIp) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = loginIp;
        this.loginCount = (this.loginCount == null ? 0 : this.loginCount) + 1;
    }
}

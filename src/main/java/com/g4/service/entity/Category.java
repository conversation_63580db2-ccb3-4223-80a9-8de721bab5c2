package com.g4.service.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 商品分类实体类
 * 管理商品分类信息，支持多级分类
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "biz_category", indexes = {
    @Index(name = "idx_category_code", columnList = "category_code", unique = true),
    @Index(name = "idx_category_name", columnList = "category_name"),
    @Index(name = "idx_parent_id", columnList = "parent_id"),
    @Index(name = "idx_status", columnList = "status"),
    @Index(name = "idx_sort", columnList = "sort")
})
public class Category extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 分类编码（唯一标识）
     */
    @NotBlank(message = "分类编码不能为空")
    @Size(max = 50, message = "分类编码长度不能超过50个字符")
    @Column(name = "category_code", nullable = false, unique = true, length = 50)
    private String categoryCode;

    /**
     * 分类名称
     */
    @NotBlank(message = "分类名称不能为空")
    @Size(max = 100, message = "分类名称长度不能超过100个字符")
    @Column(name = "category_name", nullable = false, length = 100)
    private String categoryName;

    /**
     * 父分类ID（用于构建分类树）
     */
    @Column(name = "parent_id")
    private Long parentId;

    /**
     * 分类层级（1：一级分类，2：二级分类，以此类推）
     */
    @Column(name = "level", nullable = false)
    private Integer level = 1;

    /**
     * 分类路径（用于快速查询，格式：/1/2/3/）
     */
    @Size(max = 500, message = "分类路径长度不能超过500个字符")
    @Column(name = "path", length = 500)
    private String path;

    /**
     * 分类图标
     */
    @Size(max = 500, message = "分类图标长度不能超过500个字符")
    @Column(name = "icon", length = 500)
    private String icon;

    /**
     * 分类图片
     */
    @Size(max = 500, message = "分类图片长度不能超过500个字符")
    @Column(name = "image", length = 500)
    private String image;

    /**
     * 排序号
     */
    @Column(name = "sort")
    private Integer sort = 0;

    /**
     * 分类状态（0：正常，1：禁用）
     */
    @Column(name = "status", nullable = false)
    private Integer status = 0;

    /**
     * 是否显示（0：否，1：是）
     */
    @Column(name = "is_visible")
    private Boolean isVisible = true;

    /**
     * 商品数量
     */
    @Column(name = "product_count")
    private Integer productCount = 0;

    /**
     * 分类描述
     */
    @Size(max = 500, message = "分类描述长度不能超过500个字符")
    @Column(name = "description", length = 500)
    private String description;

    /**
     * SEO关键词
     */
    @Size(max = 200, message = "SEO关键词长度不能超过200个字符")
    @Column(name = "keywords", length = 200)
    private String keywords;

    /**
     * SEO描述
     */
    @Size(max = 500, message = "SEO描述长度不能超过500个字符")
    @Column(name = "seo_description", length = 500)
    private String seoDescription;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    @Column(name = "remark", length = 500)
    private String remark;

    /**
     * 子分类列表
     */
    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id")
    @JsonIgnore
    private List<Category> children;

    /**
     * 分类商品列表
     */
    @OneToMany(mappedBy = "category", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonIgnore
    private List<Product> products;

    // ========== 便捷方法 ==========

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return status != null && status == 0;
    }

    /**
     * 是否为根分类
     */
    public boolean isRoot() {
        return parentId == null || parentId == 0;
    }

    /**
     * 是否为叶子分类
     */
    public boolean isLeaf() {
        return children == null || children.isEmpty();
    }

    /**
     * 增加商品数量
     */
    public void increaseProductCount() {
        this.productCount = (this.productCount == null ? 0 : this.productCount) + 1;
    }

    /**
     * 减少商品数量
     */
    public void decreaseProductCount() {
        this.productCount = Math.max(0, (this.productCount == null ? 0 : this.productCount) - 1);
    }

    /**
     * 构建分类路径
     */
    public void buildPath() {
        if (isRoot()) {
            this.path = "/" + this.getId() + "/";
        } else {
            // 这里需要根据父分类的路径来构建，实际实现时需要查询父分类
            this.path = "/" + this.parentId + "/" + this.getId() + "/";
        }
    }
}

package com.g4.service.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

/**
 * 应用关闭监听器
 * 在应用关闭时执行清理操作
 */
@Slf4j
@Component
public class ApplicationShutdownListener implements ApplicationListener<ContextClosedEvent> {

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
        try {
            log.info("应用开始关闭，执行清理操作...");
            
            // 执行清理操作
            performCleanup();
            
            log.info("应用关闭完成，所有清理操作已执行");
        } catch (Exception e) {
            log.error("应用关闭时清理操作失败", e);
        }
    }

    /**
     * 执行清理操作
     */
    private void performCleanup() {
        // 1. 清理缓存
        cleanupCache();

        // 2. 关闭数据库连接
        closeDatabaseConnections();

        // 3. 清理临时文件
        cleanupTempFiles();

        // 4. 关闭第三方服务连接
        closeThirdPartyConnections();

        // 5. 保存运行状态
        saveApplicationState();
    }

    /**
     * 清理缓存
     */
    private void cleanupCache() {
        try {
            log.info("清理缓存...");
            // 这里可以添加缓存清理逻辑
            log.info("缓存清理完成");
        } catch (Exception e) {
            log.error("缓存清理失败", e);
        }
    }

    /**
     * 关闭数据库连接
     */
    private void closeDatabaseConnections() {
        try {
            log.info("关闭数据库连接...");
            // 这里可以添加数据库连接关闭逻辑
            log.info("数据库连接关闭完成");
        } catch (Exception e) {
            log.error("数据库连接关闭失败", e);
        }
    }

    /**
     * 清理临时文件
     */
    private void cleanupTempFiles() {
        try {
            log.info("清理临时文件...");
            // 这里可以添加临时文件清理逻辑
            log.info("临时文件清理完成");
        } catch (Exception e) {
            log.error("临时文件清理失败", e);
        }
    }

    /**
     * 关闭第三方服务连接
     */
    private void closeThirdPartyConnections() {
        try {
            log.info("关闭第三方服务连接...");
            // 这里可以添加第三方服务连接关闭逻辑
            log.info("第三方服务连接关闭完成");
        } catch (Exception e) {
            log.error("第三方服务连接关闭失败", e);
        }
    }

    /**
     * 保存应用状态
     */
    private void saveApplicationState() {
        try {
            log.info("保存应用状态...");
            // 这里可以添加应用状态保存逻辑
            log.info("应用状态保存完成");
        } catch (Exception e) {
            log.error("应用状态保存失败", e);
        }
    }
}

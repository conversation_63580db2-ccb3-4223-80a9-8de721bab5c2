package com.g4.service.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 应用启动监听器
 * 在应用启动完成后执行初始化操作
 */
@Slf4j
@Component
public class ApplicationStartupListener implements ApplicationListener<ApplicationReadyEvent> {

    @Autowired
    private Environment environment;

    @Value("${server.port:8080}")
    private String port;

    @Value("${server.servlet.context-path:}")
    private String contextPath;

    @Value("${spring.application.name:shop-service}")
    private String applicationName;

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        try {
            // 打印启动成功信息
            printStartupInfo();
            
            // 执行初始化操作
            performInitialization();
            
            log.info("应用启动完成，所有初始化操作已执行");
        } catch (Exception e) {
            log.error("应用启动后初始化失败", e);
        }
    }

    /**
     * 打印启动信息
     */
    private void printStartupInfo() {
        try {
            String hostAddress = InetAddress.getLocalHost().getHostAddress();
            String hostName = InetAddress.getLocalHost().getHostName();
            
            log.info("\n" +
                    "==================================================\n" +
                    "  应用启动成功！\n" +
                    "  应用名称: {}\n" +
                    "  主机名称: {}\n" +
                    "  主机地址: {}\n" +
                    "  访问端口: {}\n" +
                    "  上下文路径: {}\n" +
                    "  本地访问: http://localhost:{}{}\n" +
                    "  外部访问: http://{}:{}{}\n" +
                    "  环境配置: {}\n" +
                    "  Java版本: {}\n" +
                    "  启动时间: {}\n" +
                    "==================================================",
                    applicationName,
                    hostName,
                    hostAddress,
                    port,
                    contextPath.isEmpty() ? "/" : contextPath,
                    port,
                    contextPath,
                    hostAddress,
                    port,
                    contextPath,
                    environment.getActiveProfiles().length > 0 ? 
                        String.join(",", environment.getActiveProfiles()) : "default",
                    System.getProperty("java.version"),
                    new java.util.Date()
            );

            // 打印Swagger文档地址（如果启用）
            if (isSwaggerEnabled()) {
                log.info("Swagger文档地址: http://localhost:{}{}/swagger-ui/index.html", port, contextPath);
            }

            // 打印健康检查地址（如果启用）
            if (isActuatorEnabled()) {
                log.info("健康检查地址: http://localhost:{}{}/actuator/health", port, contextPath);
            }

        } catch (UnknownHostException e) {
            log.warn("无法获取主机信息", e);
        }
    }

    /**
     * 执行初始化操作
     */
    private void performInitialization() {
        log.info("开始执行应用初始化操作...");

        // 1. 检查数据库连接
        checkDatabaseConnection();

        // 2. 检查Redis连接
        checkRedisConnection();

        // 3. 初始化缓存
        initializeCache();

        // 4. 预热数据
        preloadData();

        // 5. 检查第三方服务
        checkThirdPartyServices();

        log.info("应用初始化操作完成");
    }

    /**
     * 检查数据库连接
     */
    private void checkDatabaseConnection() {
        try {
            log.info("检查数据库连接...");
            // 这里可以添加数据库连接检查逻辑
            // 例如：执行一个简单的查询
            log.info("数据库连接正常");
        } catch (Exception e) {
            log.error("数据库连接检查失败", e);
        }
    }

    /**
     * 检查Redis连接
     */
    private void checkRedisConnection() {
        try {
            log.info("检查Redis连接...");
            // 这里可以添加Redis连接检查逻辑
            // 例如：执行ping命令
            log.info("Redis连接正常");
        } catch (Exception e) {
            log.error("Redis连接检查失败", e);
        }
    }

    /**
     * 初始化缓存
     */
    private void initializeCache() {
        try {
            log.info("初始化缓存...");
            // 这里可以添加缓存初始化逻辑
            // 例如：预加载一些常用数据到缓存
            log.info("缓存初始化完成");
        } catch (Exception e) {
            log.error("缓存初始化失败", e);
        }
    }

    /**
     * 预热数据
     */
    private void preloadData() {
        try {
            log.info("预热数据...");
            // 这里可以添加数据预热逻辑
            // 例如：预加载字典数据、配置数据等
            log.info("数据预热完成");
        } catch (Exception e) {
            log.error("数据预热失败", e);
        }
    }

    /**
     * 检查第三方服务
     */
    private void checkThirdPartyServices() {
        try {
            log.info("检查第三方服务...");
            // 这里可以添加第三方服务检查逻辑
            // 例如：检查支付接口、短信接口等
            log.info("第三方服务检查完成");
        } catch (Exception e) {
            log.error("第三方服务检查失败", e);
        }
    }

    /**
     * 检查Swagger是否启用
     */
    private boolean isSwaggerEnabled() {
        return environment.getProperty("springdoc.swagger-ui.enabled", Boolean.class, true);
    }

    /**
     * 检查Actuator是否启用
     */
    private boolean isActuatorEnabled() {
        return environment.getProperty("management.endpoints.web.exposure.include", String.class, "")
                .contains("health");
    }
}

package com.g4.service.health;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuator.health.Health;
import org.springframework.boot.actuator.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 自定义健康检查指示器
 * 用于检查应用的健康状态
 */
@Slf4j
@Component
public class CustomHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        try {
            // 检查各个组件的健康状态
            Map<String, Object> details = new HashMap<>();
            
            // 检查数据库
            boolean databaseHealthy = checkDatabase();
            details.put("database", databaseHealthy ? "UP" : "DOWN");
            
            // 检查Redis
            boolean redisHealthy = checkRedis();
            details.put("redis", redisHealthy ? "UP" : "DOWN");
            
            // 检查磁盘空间
            boolean diskHealthy = checkDiskSpace();
            details.put("diskSpace", diskHealthy ? "UP" : "DOWN");
            
            // 检查内存使用
            Map<String, Object> memoryInfo = checkMemory();
            details.put("memory", memoryInfo);
            
            // 检查第三方服务
            boolean thirdPartyHealthy = checkThirdPartyServices();
            details.put("thirdPartyServices", thirdPartyHealthy ? "UP" : "DOWN");
            
            // 添加应用信息
            details.put("application", getApplicationInfo());
            
            // 判断整体健康状态
            boolean overallHealthy = databaseHealthy && redisHealthy && diskHealthy && thirdPartyHealthy;
            
            if (overallHealthy) {
                return Health.up().withDetails(details).build();
            } else {
                return Health.down().withDetails(details).build();
            }
            
        } catch (Exception e) {
            log.error("健康检查失败", e);
            return Health.down()
                    .withException(e)
                    .withDetail("error", "健康检查执行失败")
                    .build();
        }
    }

    /**
     * 检查数据库健康状态
     */
    private boolean checkDatabase() {
        try {
            // 这里可以添加数据库连接检查逻辑
            // 例如：执行一个简单的查询
            log.debug("检查数据库健康状态");
            return true; // 假设数据库正常
        } catch (Exception e) {
            log.error("数据库健康检查失败", e);
            return false;
        }
    }

    /**
     * 检查Redis健康状态
     */
    private boolean checkRedis() {
        try {
            // 这里可以添加Redis连接检查逻辑
            // 例如：执行ping命令
            log.debug("检查Redis健康状态");
            return true; // 假设Redis正常
        } catch (Exception e) {
            log.error("Redis健康检查失败", e);
            return false;
        }
    }

    /**
     * 检查磁盘空间
     */
    private boolean checkDiskSpace() {
        try {
            // 检查磁盘可用空间
            java.io.File root = new java.io.File("/");
            long totalSpace = root.getTotalSpace();
            long freeSpace = root.getFreeSpace();
            double freePercentage = (double) freeSpace / totalSpace * 100;
            
            log.debug("磁盘空间检查 - 总空间: {}GB, 可用空间: {}GB, 可用百分比: {:.2f}%",
                    totalSpace / (1024 * 1024 * 1024),
                    freeSpace / (1024 * 1024 * 1024),
                    freePercentage);
            
            // 如果可用空间小于10%，认为不健康
            return freePercentage > 10.0;
        } catch (Exception e) {
            log.error("磁盘空间检查失败", e);
            return false;
        }
    }

    /**
     * 检查内存使用情况
     */
    private Map<String, Object> checkMemory() {
        Map<String, Object> memoryInfo = new HashMap<>();
        try {
            Runtime runtime = Runtime.getRuntime();
            long maxMemory = runtime.maxMemory();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            
            memoryInfo.put("maxMemory", formatBytes(maxMemory));
            memoryInfo.put("totalMemory", formatBytes(totalMemory));
            memoryInfo.put("usedMemory", formatBytes(usedMemory));
            memoryInfo.put("freeMemory", formatBytes(freeMemory));
            memoryInfo.put("usedPercentage", String.format("%.2f%%", (double) usedMemory / maxMemory * 100));
            memoryInfo.put("status", usedMemory < maxMemory * 0.9 ? "UP" : "WARNING");
            
        } catch (Exception e) {
            log.error("内存检查失败", e);
            memoryInfo.put("status", "DOWN");
            memoryInfo.put("error", e.getMessage());
        }
        return memoryInfo;
    }

    /**
     * 检查第三方服务
     */
    private boolean checkThirdPartyServices() {
        try {
            // 这里可以添加第三方服务检查逻辑
            // 例如：检查支付接口、短信接口等
            log.debug("检查第三方服务健康状态");
            return true; // 假设第三方服务正常
        } catch (Exception e) {
            log.error("第三方服务健康检查失败", e);
            return false;
        }
    }

    /**
     * 获取应用信息
     */
    private Map<String, Object> getApplicationInfo() {
        Map<String, Object> appInfo = new HashMap<>();
        try {
            appInfo.put("name", "shop-service");
            appInfo.put("version", "1.0.0");
            appInfo.put("javaVersion", System.getProperty("java.version"));
            appInfo.put("osName", System.getProperty("os.name"));
            appInfo.put("osVersion", System.getProperty("os.version"));
            appInfo.put("startTime", java.time.LocalDateTime.now().toString());
            appInfo.put("uptime", getUptime());
        } catch (Exception e) {
            log.error("获取应用信息失败", e);
            appInfo.put("error", e.getMessage());
        }
        return appInfo;
    }

    /**
     * 获取运行时间
     */
    private String getUptime() {
        long uptimeMillis = java.lang.management.ManagementFactory.getRuntimeMXBean().getUptime();
        long seconds = uptimeMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        return String.format("%d天 %d小时 %d分钟 %d秒", 
                days, hours % 24, minutes % 60, seconds % 60);
    }

    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
}
